import request from '@/utils/request'

// 查询打印模板列表
export function listHiprint(query) {
  return request({
    url: '/system/hiprint/list',
    method: 'get',
    params: query
  })
}

// 查询打印模板详细
export function getHiprint(id) {
  return request({
    url: '/system/hiprint/' + id,
    method: 'get'
  })
}

// 新增打印模板
export function addHiprint(data) {
  return request({
    url: '/system/hiprint',
    method: 'post',
    data: data
  })
}

// 修改打印模板
export function updateHiprint(data) {
  return request({
    url: '/system/hiprint',
    method: 'put',
    data: data
  })
}

// 删除打印模板
export function delHiprint(id) {
  return request({
    url: '/system/hiprint/' + id,
    method: 'delete'
  })
}
