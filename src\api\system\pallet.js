import request from '@/utils/request'

// 查询栈板信息列表
export function listPallet(query) {
  return request({
    url: '/system/pallet/list',
    method: 'get',
    params: query
  })
}

// 查询栈板信息详细
export function getPallet(id) {
  return request({
    url: '/system/pallet/' + id,
    method: 'get'
  })
}

// 新增栈板信息
export function addPallet(data) {
  return request({
    url: '/system/pallet',
    method: 'post',
    data: data
  })
}

// 修改栈板信息
export function updatePallet(data) {
  return request({
    url: '/system/pallet',
    method: 'put',
    data: data
  })
}

// 删除栈板信息
export function delPallet(id) {
  return request({
    url: '/system/pallet/' + id,
    method: 'delete'
  })
}
