<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接受水平" prop="acceptanceLevel">
        <el-input
          v-model="queryParams.acceptanceLevel"
          placeholder="请输入接受水平"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="描述" prop="destructiveTestNum">
        <el-input
          v-model="queryParams.destructiveTestNum"
          placeholder="请输入描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="附件" prop="attachment">
        <el-input
          v-model="queryParams.attachment"
          placeholder="请输入附件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下限值类型" prop="lowerType">
        <el-select v-model="queryParams.lowerType" placeholder="请选择下限值类型" clearable>
          <el-option
            v-for="dict in dict.type.compare_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上限值类型" prop="upperType">
        <el-select v-model="queryParams.upperType" placeholder="请选择上限值类型" clearable>
          <el-option
            v-for="dict in dict.type.compare_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检验设备" prop="inspectionEq">
        <el-input
          v-model="queryParams.inspectionEq"
          placeholder="请输入检验设备"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验方法" prop="inspectionMethod">
        <el-input
          v-model="queryParams.inspectionMethod"
          placeholder="请输入检验方法"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位" prop="itemUnit">
        <el-input
          v-model="queryParams.itemUnit"
          placeholder="请输入单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属工厂" prop="siteId">
        <el-input
          v-model="queryParams.siteId"
          placeholder="请输入所属工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验等级" prop="inspectionLevel">
        <el-input
          v-model="queryParams.inspectionLevel"
          placeholder="请输入检验等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分析方法" prop="analysisMethod">
        <el-input
          v-model="queryParams.analysisMethod"
          placeholder="请输入分析方法"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准值" prop="standardValue">
        <el-input
          v-model="queryParams.standardValue"
          placeholder="请输入标准值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上限值" prop="upperLimit">
        <el-input
          v-model="queryParams.upperLimit"
          placeholder="请输入上限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下限值" prop="lowerLimit">
        <el-input
          v-model="queryParams.lowerLimit"
          placeholder="请输入下限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="技术标准" prop="technicalStandard">
        <el-input
          v-model="queryParams.technicalStandard"
          placeholder="请输入技术标准"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-input
          v-model="queryParams.dataSource"
          placeholder="请输入数据来源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验组数" prop="inspectionGroup">
        <el-input
          v-model="queryParams.inspectionGroup"
          placeholder="请输入检验组数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="误差范围下限" prop="errorRangeLowerLimit">
        <el-input
          v-model="queryParams.errorRangeLowerLimit"
          placeholder="请输入误差范围下限"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="误差范围上限" prop="errorRangeUpperLimit">
        <el-input
          v-model="queryParams.errorRangeUpperLimit"
          placeholder="请输入误差范围上限"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:item:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quality:item:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quality:item:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:item:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="itemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="接受水平" align="center" prop="acceptanceLevel" />
      <el-table-column label="描述" align="center" prop="destructiveTestNum" />
      <el-table-column label="项目名称" align="center" prop="itemName" />
      <el-table-column label="附件" align="center" prop="attachment" />
      <el-table-column label="下限值类型" align="center" prop="lowerType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.compare_state" :value="scope.row.lowerType"/>
        </template>
      </el-table-column>
      <el-table-column label="上限值类型" align="center" prop="upperType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.compare_state" :value="scope.row.upperType"/>
        </template>
      </el-table-column>
      <el-table-column label="检验设备" align="center" prop="inspectionEq" />
      <el-table-column label="检验方法" align="center" prop="inspectionMethod" />
      <el-table-column label="单位" align="center" prop="itemUnit" />
      <el-table-column label="所属工厂" align="center" prop="siteId" />
      <el-table-column label="项目编码" align="center" prop="itemCode" />
      <el-table-column label="所属类型" align="center" prop="inspectionClassType" />
      <el-table-column label="检验等级" align="center" prop="inspectionLevel" />
      <el-table-column label="分析方法" align="center" prop="analysisMethod" />
      <el-table-column label="标准值" align="center" prop="standardValue" />
      <el-table-column label="上限值" align="center" prop="upperLimit" />
      <el-table-column label="下限值" align="center" prop="lowerLimit" />
      <el-table-column label="技术标准" align="center" prop="technicalStandard" />
      <el-table-column label="结果类型" align="center" prop="resultType" />
      <el-table-column label="数据来源" align="center" prop="dataSource" />
      <el-table-column label="检验组数" align="center" prop="inspectionGroup" />
      <el-table-column label="误差范围下限" align="center" prop="errorRangeLowerLimit" />
      <el-table-column label="误差范围上限" align="center" prop="errorRangeUpperLimit" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:item:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:item:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改IQC检验项目
对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="接受水平" prop="acceptanceLevel" style="width: 240px;">
          <el-input v-model="form.acceptanceLevel" placeholder="请输入接受水平" />
        </el-form-item>
        <el-form-item label="描述" prop="destructiveTestNum" style="width: 240px;">
          <el-input v-model="form.destructiveTestNum" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="项目名称" prop="itemName" style="width: 240px;">
          <el-input v-model="form.itemName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment" style="width: 240px;">
          <el-input v-model="form.attachment" placeholder="请输入附件" />
        </el-form-item>
        <el-form-item label="下限值类型" prop="lowerType" style="width: 240px;">
          <el-select v-model="form.lowerType" placeholder="请选择下限值类型" style="width: 240px;">
            <el-option
              v-for="dict in dict.type.compare_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上限值类型" prop="upperType" style="width: 240px;">
          <el-select v-model="form.upperType" placeholder="请选择上限值类型" style="width: 240px;">
            <el-option
              v-for="dict in dict.type.compare_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检验设备" prop="inspectionEq" style="width: 240px;">
          <el-input v-model="form.inspectionEq" placeholder="请输入检验设备" />
        </el-form-item>
        <el-form-item label="检验方法" prop="inspectionMethod" style="width: 240px;">
          <el-input v-model="form.inspectionMethod" placeholder="请输入检验方法" />
        </el-form-item>
        <el-form-item label="单位" prop="itemUnit" style="width: 240px;">
          <el-input v-model="form.itemUnit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="所属工厂" prop="siteId" style="width: 240px;">
          <el-input v-model="form.siteId" placeholder="请输入所属工厂" />
        </el-form-item>
        <el-form-item label="项目编码" prop="itemCode" style="width: 240px;">
          <el-input v-model="form.itemCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="检验等级" prop="inspectionLevel" style="width: 240px;">
          <el-input v-model="form.inspectionLevel" placeholder="请输入检验等级" />
        </el-form-item>
        <el-form-item label="分析方法" prop="analysisMethod" style="width: 240px;">
          <el-input v-model="form.analysisMethod" placeholder="请输入分析方法" />
        </el-form-item>
        <el-form-item label="标准值" prop="standardValue" style="width: 240px;">
          <el-input v-model="form.standardValue" placeholder="请输入标准值" />
        </el-form-item>
        <el-form-item label="上限值" prop="upperLimit" style="width: 240px;">
          <el-input v-model="form.upperLimit" placeholder="请输入上限值" />
        </el-form-item>
        <el-form-item label="下限值" prop="lowerLimit" style="width: 240px;">
          <el-input v-model="form.lowerLimit" placeholder="请输入下限值" />
        </el-form-item>
        <el-form-item label="技术标准" prop="technicalStandard" style="width: 240px;">
          <el-input v-model="form.technicalStandard" placeholder="请输入技术标准" />
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource" style="width: 240px;">
          <el-input v-model="form.dataSource" placeholder="请输入数据来源" />
        </el-form-item>
        <el-form-item label="检验组数" prop="inspectionGroup" style="width: 240px;">
          <el-input v-model="form.inspectionGroup" placeholder="请输入检验组数" />
        </el-form-item>
        <el-form-item label="误差范围下限" prop="errorRangeLowerLimit" style="width: 240px;">
          <el-input v-model="form.errorRangeLowerLimit" placeholder="请输入误差范围下限" />
        </el-form-item>
        <el-form-item label="误差范围上限" prop="errorRangeUpperLimit" style="width: 240px;">
          <el-input v-model="form.errorRangeUpperLimit" placeholder="请输入误差范围上限" />
        </el-form-item>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listItem, getItem, delItem, addItem, updateItem } from "@/api/quality/item";

export default {
  name: "Item",
  dicts: ['compare_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // IQC检验项目表格数据
      itemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
        itemName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        siteId: [
          { required: true, message: "所属工厂不能为空", trigger: "blur" }
        ],
        itemCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" }
        ],
        inspectionClassType: [
          { required: true, message: "所属类型不能为空", trigger: "change" }
        ],
        inspectionLevel: [
          { required: true, message: "检验等级不能为空", trigger: "blur" }
        ],
        analysisMethod: [
          { required: true, message: "分析方法不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询IQC检验项目
列表 */
    getList() {
      this.loading = true;
      listItem(this.queryParams).then(response => {
        this.itemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加IQC检验项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getItem(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改IQC检验项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateItem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除IQC检验项目编号为"' + ids + '"的数据项？').then(function() {
        return delItem(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('quality/item/export', {
        ...this.queryParams
      }, `IQC检验项目
_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
