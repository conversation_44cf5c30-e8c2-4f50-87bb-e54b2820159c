<template>
  <el-table
    height="62vh"
    v-loading="loading"
    :data="allotBoxList"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" />
    <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
    <el-table-column label="调拨单号" align="center" prop="allotNo" />
    <!-- <el-table-column label="调拨主表id" align="center" prop="allotId" /> -->
    <!-- <el-table-column label="调拨明细id" align="center" prop="detailId" /> -->
    <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
    <el-table-column label="物料编码" align="center" prop="materialCode" />
    <el-table-column label="物料名称" align="center" prop="materialName" />
    <el-table-column label="规格型号" align="center" prop="specification" />
    <el-table-column label="单位" align="center" prop="materialUnit" />
    <el-table-column label="数量" align="center" prop="qty" />
    <el-table-column label="箱号" align="center" prop="boxNo" />
    <el-table-column label="箱二维码" align="center" prop="qrCode" />
    <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
    <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
    <el-table-column label="仓库名称" align="center" prop="warehouseName" />
    <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
    <el-table-column label="库区编码" align="center" prop="areaCode" />
    <el-table-column label="库区名称" align="center" prop="areaName" />
    <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
    <el-table-column label="库位编码" align="center" prop="locationCode" />
    <el-table-column label="库位名称" align="center" prop="locationName" />
    <el-table-column label="批次" align="center" prop="batchNo" />
    <el-table-column label="是否暂存；" align="center" prop="isStaging" />
    <!-- <el-table-column label="供应商id" align="center" prop="supplierId" /> -->
    <el-table-column label="供应商编码" align="center" prop="supplierCode" />
    <el-table-column label="供应商名称" align="center" prop="supplierName" />
    <el-table-column label="备注" align="center" prop="remark" />
    <el-table-column label="组织" align="center" prop="comId" />
    <!-- <el-table-column
      label="操作"
      align="center"
      class-name="small-padding fixed-width"
    >
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="text"
          icon="el-icon-edit"
          @click="handleUpdate(scope.row)"
          v-hasPermi="['system:allotBox:edit']"
          >修改</el-button
        >
        <el-button
          size="mini"
          type="text"
          icon="el-icon-delete"
          @click="handleDelete(scope.row)"
          v-hasPermi="['system:allotBox:remove']"
          >删除</el-button
        >
      </template>
    </el-table-column> -->
  </el-table>
</template>

<script>
import {
  listAllotBox,
  getAllotBox,
  delAllotBox,
  addAllotBox,
  updateAllotBox,
} from "@/api/system/allotBox";
// import {
//   listStock_box,
//   getStock_box,
//   delStock_box,
//   addStock_box,
//   updateStock_box,
// } from "@/api/system/stockOutBox";

export default {
  props: ["allot_detail_id"],
  name: "AllotBox",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调拨标签表格数据
      allotBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allotNo: null,
        allotId: null,
        detailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        comId: null,
      },
      // 缓存数据
      // cacheData: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        allotNo: [
          { required: true, message: "调拨单号不能为空", trigger: "blur" },
        ],
        allotId: [
          { required: true, message: "调拨主表id不能为空", trigger: "blur" },
        ],
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询调拨标签列表 */
    getList() {
      // 判断 allot_detail_id 是否存在且不为空字符串
      if (this.allot_detail_id && this.allot_detail_id != "") {
        this.loading = true;
        console.log("this.allot_detail_id", this.allot_detail_id);
        // 如果有值，设置查询参数中的 detailId
        this.queryParams.detailId = this.allot_detail_id;
        listAllotBox(this.queryParams).then((response) => {
          this.allotBoxList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
      this.loading = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        allotNo: null,
        allotId: null,
        detailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        // 调拨标签表格数据
        allotBoxList: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调拨标签";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAllotBox(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改调拨标签";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAllotBox(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAllotBox(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除调拨标签编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAllotBox(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/allotBox/export",
        {
          ...this.queryParams,
        },
        `调拨标签_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
