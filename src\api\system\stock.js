import request from '@/utils/request'

// 查询盘点主表列表
export function listStock(query) {
  return request({
    url: '/system/stock/list',
    method: 'get',
    params: query
  })
}

// 查询盘点主表详细
export function getStock(id) {
  return request({
    url: '/system/stock/' + id,
    method: 'get'
  })
}

// 新增盘点主表
export function addStock(data) {
  return request({
    url: '/system/stock',
    method: 'post',
    data: data
  })
}

// 修改盘点主表
export function updateStock(data) {
  return request({
    url: '/system/stock',
    method: 'put',
    data: data
  })
}

// 删除盘点主表
export function delStock(id) {
  return request({
    url: '/system/stock/' + id,
    method: 'delete'
  })
}
