import request from '@/utils/request'

// 查询检验模板明细列表
export function listInfo(query) {
  return request({
    url: '/quality/info/list',
    method: 'get',
    params: query
  })
}

// 查询检验模板明细详细
export function getInfo(id) {
  return request({
    url: '/quality/info/' + id,
    method: 'get'
  })
}

// 新增检验模板明细
export function addInfo(data) {
  return request({
    url: '/quality/info',
    method: 'post',
    data: data
  })
}

// 修改检验模板明细
export function updateInfo(data) {
  return request({
    url: '/quality/info',
    method: 'put',
    data: data
  })
}

// 删除检验模板明细
export function delInfo(id) {
  return request({
    url: '/quality/info/' + id,
    method: 'delete'
  })
}
