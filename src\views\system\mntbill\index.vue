<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="88px"
      >
        <el-form-item label="保养方案编码" prop="mntSpId">
          <el-input
            v-model="queryParams.mntSpId"
            placeholder="请输入保养方案编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备编码" prop="equipCode">
          <el-input
            v-model="queryParams.equipCode"
            placeholder="请输入设备编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="保养人员" prop="mntUser">
          <el-input
            v-model="queryParams.mntUser"
            placeholder="请输入保养人员"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="保养时间" prop="mntTime">
          <el-date-picker
            clearable
            v-model="queryParams.mntTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保养时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="间隔天数" prop="intervalDay">
          <el-input
            v-model="queryParams.intervalDay"
            placeholder="请输入间隔天数"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="下次保养时间" prop="nextMntTime">
          <el-date-picker
            clearable
            v-model="queryParams.nextMntTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择下次保养时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="组织" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入组织"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:mntbill:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:mntbill:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:mntbill:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:mntbill:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="billList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column label="保养方案编码" align="center" prop="mntSpId" />
        <el-table-column label="设备编码" align="center" prop="equipCode" />
        <el-table-column label="保养人员" align="center" prop="mntUser" />
        <el-table-column
          label="保养时间"
          align="center"
          prop="mntTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.mntTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="间隔天数" align="center" prop="intervalDay" />
        <el-table-column
          label="下次保养时间"
          align="center"
          prop="nextMntTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.nextMntTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="操作"
          align="center" 
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:mntbill:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:mntbill:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改设备保养单对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
        <el-row>
          <el-col :span="12">
          <el-form-item label="保养方案编码" prop="mntSpId">
            <el-input v-model="form.mntSpId" placeholder="请输入保养方案ID" style="width:400px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="设备编码" prop="equipCode">
            <el-input v-model="form.equipCode" placeholder="请输入设备编码" style="width:400px"/>
          </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
          <el-form-item label="保养人员" prop="mntUser">
            <el-input v-model="form.mntUser" placeholder="请输入保养人员" style="width :400px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="保养时间" prop="mntTime">
            <el-date-picker
              clearable
              v-model="form.mntTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择保养时间"
              style="width :400px"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
          <el-form-item label="间隔天数" prop="intervalDay">
            <el-input v-model="form.intervalDay" placeholder="请输入间隔天数" style="width:400px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="下次保养时间" prop="nextMntTime">
            <el-date-picker
              clearable
              v-model="form.nextMntTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择下次保养时间"
              style="width:400px"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
        </el-row>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>

        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listBill,
  getBill,
  delBill,
  addBill,
  updateBill,
} from "@/api/system/mntbill";

export default {
  name: "Bill",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备保养单表格数据
      billList: [],
      // 设备编码数组
      codes: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mntSpId: null,
        equipCode: null,
        mntUser: null,
        mntTime: null,
        intervalDay: null,
        nextMntTime: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mntSpId: [
          { required: true, trigger: "blur", message: "保养单编码不能为空" },
        ],
        equipCode: [
          { required: true, trigger: "blur", message: "设备编码不能为空" },
        ],
        mntUser: [
          { required: true, trigger: "blur", message: "保养人不能为空" },
        ],
        supplierEmail: [
          { required: true, trigger: "blur", message: "供应商邮箱不能为空" },
        ],
        supplierAddress: [
          { required: true, trigger: "blur", message: "供应商地址不能为空" },
        ],
        phone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备保养单列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then((response) => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mntSpId: null,
        equipCode: null,
        mntUser: null,
        mntTime: null,
        intervalDay: null,
        nextMntTime: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.codes = selection.map((item) => item.equipCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备保养单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBill(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备保养单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBill(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.equipCode || this.codes;
      this.$modal
        .confirm('是否确认删除设备编码为"' + codes + '"的数据项？')
        .then(function () {
          return delBill(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/mntbill/export",
        {
          ...this.queryParams,
        },
        `bill_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
