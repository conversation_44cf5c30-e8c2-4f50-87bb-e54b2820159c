/**
 * 数组通用排序工具
 * @param {Array} arr - 待排序的数组
 * @param {String} field - 排序依据的字段名
 * @param {Boolean} isAsc - 是否升序（默认降序）
 * @returns {Array} 排序后的新数组
 */
export function sortArrayByField(arr, field, isAsc = false) {

 
    
    // 复制原数组避免修改
    const newArr = [...arr];

    return newArr.sort((a, b) => {
        // 获取字段值
        const valueA = a[field];
        const valueB = b[field];

        // 处理日期类型
        if (typeof valueA === 'string' && !isNaN(Date.parse(valueA)) &&
            typeof valueB === 'string' && !isNaN(Date.parse(valueB))) {
            return isAsc
                ? new Date(valueA) - new Date(valueB)
                : new Date(valueB) - new Date(valueA);
        }

        // 处理数值类型
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return isAsc ? valueA - valueB : valueB - valueA;
        }

        // 处理字符串类型
        if (typeof valueA === 'string' && typeof valueB === 'string') {
            return isAsc
                ? valueA.localeCompare(valueB)
                : valueB.localeCompare(valueA);
        }

        // 默认不排序
        return 0;
    });
}