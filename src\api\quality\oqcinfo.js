import request from '@/utils/request'

// 查询OQC检验配置明细列表
export function listOqcinfo(query) {
  return request({
    url: '/quality/oqcinfo/list',
    method: 'get',
    params: query
  })
}

// 查询OQC检验配置明细详细
export function getOqcinfo(id) {
  return request({
    url: '/quality/oqcinfo/' + id,
    method: 'get'
  })
}

// 新增OQC检验配置明细
export function addOqcinfo(data) {
  return request({
    url: '/quality/oqcinfo',
    method: 'post',
    data: data
  })
}

// 修改OQC检验配置明细
export function updateOqcinfo(data) {
  return request({
    url: '/quality/oqcinfo',
    method: 'put',
    data: data
  })
}

// 删除OQC检验配置明细
export function delOqcinfo(id) {
  return request({
    url: '/quality/oqcinfo/' + id,
    method: 'delete'
  })
}
