import request from '@/utils/request'

// 查询oqc质检单据列表
export function listOqcbill(query) {
  return request({
    url: '/quality/oqcbill/list',
    method: 'get',
    params: query
  })
}

// 查询oqc质检单据详细
export function getOqcbill(id) {
  return request({
    url: '/quality/oqcbill/' + id,
    method: 'get'
  })
}

// 新增oqc质检单据
export function addOqcbill(data) {
  return request({
    url: '/quality/oqcbill',
    method: 'post',
    data: data
  })
}

// 进行oqc质检
export function addQuality(data) {
  return request({
    url: '/quality/oqcbill/addQuality',
    method: 'post',
    data: data
  })
}
// 修改oqc质检单据
export function updateOqcbill(data) {
  return request({
    url: '/quality/oqcbill',
    method: 'put',
    data: data
  })
}

// 删除oqc质检单据
export function delOqcbill(id) {
  return request({
    url: '/quality/oqcbill/' + id,
    method: 'delete'
  })
}
