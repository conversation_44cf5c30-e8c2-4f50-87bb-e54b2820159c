import request from '@/utils/request'

// 查询oqc检验结果列表
export function listOqcresult(query) {
  return request({
    url: '/quality/oqcresult/list',
    method: 'get',
    params: query
  })
}

export function listByPart(query) {
  return request({
    url: '/quality/oqcresult/listByPart',
    method: 'get',
    params: query
  })
}


// 查询oqc检验结果详细
export function getOqcresult(id) {
  return request({
    url: '/quality/oqcresult/' + id,
    method: 'get'
  })
}

// 新增oqc检验结果
export function addOqcresult(data) {
  return request({
    url: '/quality/oqcresult',
    method: 'post',
    data: data
  })
}

// 修改oqc检验结果
export function updateOqcresult(data) {
  return request({
    url: '/quality/oqcresult',
    method: 'put',
    data: data
  })
}

// 删除oqc检验结果
export function delOqcresult(id) {
  return request({
    url: '/quality/oqcresult/' + id,
    method: 'delete'
  })
}
