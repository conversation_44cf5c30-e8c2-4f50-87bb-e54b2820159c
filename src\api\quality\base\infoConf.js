import request from '@/utils/request'

// 查询IQC检验配置明细列表
export function listInfo(query) {
  return request({
    url: '/quality/infoConf/list',
    method: 'get',
    params: query
  })
}

// 查询IQC检验配置明细详细
export function getInfo(id) {
  return request({
    url: '/quality/infoConf/' + id,
    method: 'get'
  })
}

// 新增IQC检验配置明细
export function addInfo(data) {
  return request({
    url: '/quality/infoConf',
    method: 'post',
    data: data
  })
}

// 修改IQC检验配置明细
export function updateInfo(data) {
  return request({
    url: '/quality/infoConf',
    method: 'put',
    data: data
  })
}

// 删除IQC检验配置明细
export function delInfo(id) {
  return request({
    url: '/quality/infoConf/' + id,
    method: 'delete'
  })
}
