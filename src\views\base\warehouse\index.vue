<template>
  <div class="app-container">
    <div class="app-container-div">
      <div style="padding: 0px; background: #fff; border-radius: 5px">
        <el-row :gutter="20">
          <el-col :span="5" :xs="24">
            <div class="head-container">
              <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            </div>
            <el-tree :data="dataTree" node-key="id" :props="defaultProps" @node-click="handleNodeClick"
              :filter-node-method="filterNode" :expand-on-click-node="false" default-expand-all highlight-current
              ref="tree"></el-tree>
          </el-col>
          <el-col v-if="parentData.level == 'LEVEL_1' || parentData.level == null" :span="19" :xs="24">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
              label-width="68px">
              <el-form-item label="仓库编码" prop="warehouseCode">
                <el-input v-model="queryParams.warehouseCode" placeholder="请输入仓库编码" clearable
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="仓库名称" prop="warehouseName">
                <el-input v-model="queryParams.warehouseName" placeholder="请输入仓库名称" clearable
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                  v-hasPermi="['system:warehouse:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                  v-hasPermi="['system:warehouse:edit']">修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                  @click="handleDelete" v-hasPermi="['system:warehouse:remove']">删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                  v-hasPermi="['system:warehouse:export']">导出</el-button>
              </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <el-row v-if="parentData.level == 'LEVEL_1' || parentData.level == null" :gutter="10" class="mb8">
              <el-table height="62vh" v-loading="loading" :data="warehouseList"
                @selection-change="handleSelectionChange" :default-sort="{ prop: 'createTime', order: 'descending' }">
                <el-table-column type="index" width="55" align="center" />
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
                <el-table-column label="仓库名称" align="center" prop="warehouseName" />
                <el-table-column label="负责人" align="center" prop="leader" />
                <el-table-column label="联系电话" align="center" prop="phone" />
                <el-table-column label="仓库类型" align="center" prop="warehouseType" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                      v-hasPermi="['system:warehouse:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                      v-hasPermi="['system:warehouse:remove']">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
            </el-row>
          </el-col>
          <el-col v-if="parentData.level == 'LEVEL_2'" :span="19" :xs="24">
            <Area @refresh="refreshForm" :parentData="parentData" :key="parentData.id" />
          </el-col>
          <el-col v-if="parentData.level == 'LEVEL_3'" :span="19" :xs="24">
            <KuWei @refresh="refreshForm" :parentData="parentData" :key="parentData.id" />
          </el-col>
          <el-col v-if="parentData.level == 'LEVEL_4'" :span="19" :xs="24">
            <KuWei :parentData="parentData" :key="parentData.id" />
          </el-col>
        </el-row>
        <!-- 添加或修改仓库对话框 -->
        <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
          <el-form ref="form" :model="form" :rules="rules">
            <el-collapse v-model="activeNames">
              <el-collapse-item title="仓库信息" name="1">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="仓库编码" prop="warehouseCode" style="width: 240px">
                      <el-input v-model="form.warehouseCode" placeholder="请输入仓库编码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="仓库名称" prop="warehouseName" style="width: 240px">
                      <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="显示顺序" prop="orderNum" style="width: 240px">
                      <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="负责人" prop="leader" style="width: 240px">
                      <el-input v-model="form.leader" placeholder="请输入负责人" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="仓库类型" prop="warehouseType" style="width: 240px">
                      <el-select v-model="form.warehouseType" placeholder="请选择仓库类型" clearable style="width: 240px"
                        filterable>
                        <el-option v-for="dict in warehouseTypeList" :key="dict.id" :label="dict.warehouseTypeCode + '-' + dict.warehouseTypeName
                          " :value="dict.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="phone" style="width: 240px">
                      <el-input v-model="form.phone" placeholder="请输入联系电话" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="仓库标识" prop="warehouseType" style="width: 240px">
                      <el-select v-model="form.wareSign" placeholder="仓库标识" clearable style="width: 240px" filterable>

                        <el-option v-for="dict in dict.type.warehouse_sign_dict" :key="dict.value" :label="dict.label"
                          :value="dict.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              </el-collapse-item>
            </el-collapse>

          </el-form>
          <div class="demo-drawer__footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-drawer>
      </div>
    </div>
  </div>
</template>

<script>
import {
  listWarehouse,
  getWarehouse,
  delWarehouse,
  addWarehouse,
  updateWarehouse,
  listLeftWarehouse,
  getTreeList,
} from "@/api/system/warehouse";
import { listWarehouseType } from "@/api/system/warehouseType";
import Area from "../area/index.vue";
import KuWei from "../warehouse_location/index.vue";
export default {
  dicts: ["warehouse_sign_dict"],
  components: {
    Area,
    KuWei,
  },
  name: "Warehouse",
  data() {
    return {
      activeNames: ["1"],
      filterText: "",
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 仓库表格数据
      warehouseList: [],
      //仓库类型list
      warehouseTypeList: [],
      // 仓库左边表格数据
      warehouseListLeft: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      dataTree: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      //传递信息的表单
      parentData: {
        label: null,
        id: null,
        level: null
      },
      // 查询参数
      queryParams: {
        // 仓库类型下拉框数据
        warehouseTypeList: [],
        pageNum: 1,
        pageSize: 10,
        warehouseCode: null,
        warehouseName: null,
        factoryId: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
        wareSign: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        warehouseCode: [
          { required: true, trigger: "blur", message: "设备编码不能为空" },
        ],
        equipName: [
          { required: true, trigger: "blur", message: "设备名称不能为空" },
        ],
        macAddress: [
          { required: true, trigger: "blur", message: "Mac地址不能为空" },
        ],
        specification: [
          { required: true, trigger: "blur", message: "规格型号不能为空" },
        ],
        equipImage: [
          { required: true, trigger: "blur", message: "设备图片不能为空" },
        ],
        picPhone: [
          // { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        warehouseTypeList: [
          { required: true, trigger: "blur", message: "仓库类型不能为空" },
        ],
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.treeList();

    this.getWarehouseType();
    // this.getList();

  },
  methods: {
    refreshForm() {
      this.treeList();
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    handleNodeClick(e) {
      this.parentData = e;

      if (this.parentData.level == "LEVEL_1" && this.parentData.id != null) {
        this.queryParams.factoryId = this.parentData.id;
        this.getList();
      }


    },
    treeList() {

      getTreeList().then((res) => {
        this.dataTree = res;
      });
    },
    /** 查询仓库列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      this.queryParams.factoryId = this.parentData.id;
      listWarehouse(this.queryParams).then((response) => {
        this.warehouseList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.treeList();
      });
    },
    getWarehouseTypeName(id) {
      const item = this.warehouseTypeList.find((item) => {
        return item.warehouseTypeCode === id;
      });
      if (item) {
        return item.warehouseTypeName; // 如果需要返回结果
      } else {
        return null; // 或者返回其他默认值
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        warehouseCode: null,
        warehouseName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        wareSign: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    getWareType() {
      let params = {
        pageNo: 1,
        pageSize: 10000,
      };
      listWarehouseType(params).then((response) => {
        this.warehouseTypeList = response.rows;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (this.parentData.level != "LEVEL_1" || this.parentData.id == null) {
        return this.$modal.msg("请选择工厂节点");
      }
      this.reset();
      this.form.factoryId = this.parentData.id;
      this.getWareType();
      this.open = true;
      this.title = "添加仓库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getWareType();
      const id = row.id || this.ids;
      getWarehouse(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改仓库";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWarehouse(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWarehouse(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除仓库编号为"' + row.warehouseCode + '"的数据项？')
        .then(function () {
          return delWarehouse(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/warehouse/export",
        {
          ...this.queryParams,
        },
        `warehouse_${new Date().getTime()}.xlsx`
      );
    },
    /**查询仓库类型 */
    getWarehouseType() {
      listWarehouseType(this.loadAllParams).then((response) => {
        this.warehouseTypeList = response.rows;

      });
    },
    /** 查询仓库id对应的仓库类型 */
    getWarehouseTypeId(id) {
      getWarehouseType(id).then((response) => {
        this.warehouseTypeList = response.data;

      });
    },
  },
};
</script>
<style lang="scss">
.app-container {
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    // 设置颜色
    background-color: rgba(135,
        206,
        235,
        0.2); // 透明度为0.2的skyblue，作者比较喜欢的颜色
    color: #409eff; // 节点的字体颜色
    font-weight: bold; // 字体加粗
  }
}
</style>