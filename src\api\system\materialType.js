import request from '@/utils/request'

// 查询物料类型列表
export function listMaterialType(query) {
  return request({
    url: '/system/materialType/list',
    method: 'get',
    params: query
  })
}

// 查询物料类型详细
export function getMaterialType(id) {
  return request({
    url: '/system/materialType/' + id,
    method: 'get'
  })
}

// 新增物料类型
export function addMaterialType(data) {
  return request({
    url: '/system/materialType',
    method: 'post',
    data: data
  })
}

// 修改物料类型
export function updateMaterialType(data) {
  return request({
    url: '/system/materialType',
    method: 'put',
    data: data
  })
}

// 删除物料类型
export function delMaterialType(id) {
  return request({
    url: '/system/materialType/' + id,
    method: 'delete'
  })
}
