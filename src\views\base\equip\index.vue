<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="设备编码" prop="equipCode">
          <el-input v-model="queryParams.equipCode" placeholder="请输入设备编码" clearable @change="handleQuery" />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipName">
          <el-input v-model="queryParams.equipName" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="生产日期" prop="manufactorDate">
          <el-date-picker clearable v-model="queryParams.manufactorDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择生产日期">
          </el-date-picker>
        </el-form-item>

        <!-- <el-form-item label="供应商" prop="supplierCode">
          <el-input v-model="queryParams.supplierCode" placeholder="请输入供应商" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->

        <el-form-item label="供应商" prop="supplierCode">
          <el-select v-model="queryParams.supplierCode" placeholder="请选择供应商" style="width: 260px" filterable clearable>
            <el-option v-for="(item, index) in supplierList" :key="index"
              :label="item.supplierCode + '-' + item.supplierName" :value="item.supplierCode"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="负责人" prop="picName">
          <el-input v-model="queryParams.picName" placeholder="请输入负责人" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item label="Mac地址" prop="macAddress">
          <el-input v-model="queryParams.macAddress" placeholder="请输入Mac地址" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:equip:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:equip:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:equip:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:equip:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        <el-tooltip class="item" effect="dark" content="切换列表" placement="top">
          <el-button size="mini" circle icon="el-icon-s-operation" title="切换列表" @click="toggleList()" />
        </el-tooltip>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="equipList" @selection-change="handleSelectionChange"
        v-if="isTable">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备编码" align="center" prop="equipCode" />
        <el-table-column label="设备名称" align="center" prop="equipName" />
        <el-table-column label="设备类型" align="center" prop="equipType" />
        <el-table-column label="规格型号" align="center" prop="specification" :width="tableWidth(equipList.map((x) => x.specification))"/>
        <el-table-column label="设备单位" align="center" prop="equipUnit">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.equip_unit" :value="scope.row.equipUnit" />
          </template>
        </el-table-column>
        <el-table-column label="设备重量" align="center" prop="equipWeight" />
        <el-table-column label="外形尺寸" align="center" prop="equipSize" />
        <el-table-column label="生产厂家" align="center" prop="equipManufactor" />
        <el-table-column label="生产日期" align="center" prop="manufactorDate" width="180">
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.manufactorDate, "{y}-{m}-{d}")
              }}</span>
          </template>
        </el-table-column>
        <el-table-column label="供应商" align="center" prop="supplierCode" />
        <el-table-column label="采购日期" align="center" prop="purchaseDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.purchaseDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="入库日期" align="center" prop="stockInDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.stockInDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用部门" align="center" prop="departmentName" />
        <el-table-column label="负责人" align="center" prop="picName" />
        <el-table-column label="负责人手机号码" align="center" prop="picPhone" :width="tableWidth(equipList.map((x) => x.picPhone))"/>
        <el-table-column label="使用地点" align="center" prop="address" />
        <el-table-column label="设备图片" align="center" prop="equipImage" width="100">
          <template slot-scope="scope">
            <image-preview :src="scope.row.equipImage" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="设备二维码" align="center" prop="equipQr" />
        <el-table-column label="设备状态" align="center" prop="equipStatus">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.equipStatus" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="Mac地址" align="center" prop="macAddress" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:equip:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:equip:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="!isTable" class="ul-height" v-loading="loading">
        <el-row v-if="equipList.length > 0" :gutter="24" style="width: 100%;">
          <el-col :span="4" v-for="(item, index) in equipList" :key="index" :offset="index % 4 != 0 ? 2 : 0"
            class="colClass" @click.native="handleUpdate(item, index)">
            <el-card :body-style="{ padding: '0px' }" shadow="always">
              <div @click.stop class="imgClass">
                <image-preview :src="item.equipImage" :width="'100%'" :height="'100%'" />
              </div>
              <div style="padding: 14px">
                <span class="contentTitle">{{ item.equipCode }}</span><br />
                <p>
                  <span class="contentSpan">设备名称</span><br />
                  <span class="contentTitle">{{ item.equipName }}</span>
                </p>
                <p>
                  <span class="contentSpan">规格/型号</span><br />
                  <span class="contentTitle">{{ item.specification }}</span>
                </p>
                <p>
                  <span class="contentSpan">设备状态</span><br />
                  <dict-tag :options="dict.type.sys_normal_disable" :value="item.equipStatus" />
                </p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- <Empty v-else class="emptyClass"></Empty> -->
        <!-- <ul class="content-ul">
          <li
            v-for="(item, index) in equipList"
            :key="index"
            @click.stop="handleUpdate(item, index)"
          >
          <div @click.stop class="imgClass">
            <image-preview
              :src="item.equipImage"
              :width="'100%'"
              :height="'100%'"
            />
          </div>1
            
            <span class="contentTitle">{{ item.equipCode }}</span><br/>
            <p>
             <span class="contentSpan">设备名称</span><br/>
              <span class="contentTitle">{{item.equipName}}</span>
            </p>
            <p>
             <span class="contentSpan">规格/型号</span><br/>
              <span class="contentTitle">{{item.specification}}</span>
            </p>
            <p>
             <span class="contentSpan">设备状态</span><br/>
             <dict-tag
                  :options="dict.type.sys_normal_disable"
                  :value="item.equipStatus"
                />
            </p>
          </li>
        </ul> -->
      </div>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改设备信息对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-row>
            <el-col :span="8">
              <el-form-item label="设备编码" prop="equipCode">
                <el-input v-model="form.equipCode" placeholder="请输入设备编码" style="width: 260px" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名称" prop="equipName">
                <el-input v-model="form.equipName" placeholder="请输入设备名称" style="width: 260px" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格型号" prop="specification">
                <el-input v-model="form.specification" placeholder="请输入规格型号" style="width: 260px" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="设备单位" prop="equipUnit">
                <el-select v-model="form.equipUnit" placeholder="请选择设备单位" style="width: 260px">
                  <el-option v-for="dict in dict.type.equip_unit" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备重量(KG)" prop="equipWeight">
                <el-input v-model="form.equipWeight" placeholder="请输入设备重量" style="width: 260px" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="外形尺寸" prop="equipSize">
                <el-input v-model="form.equipSize" placeholder="请输入外形尺寸" style="width: 260px" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="生产厂家" prop="equipManufactor">
                <el-input v-model="form.equipManufactor" placeholder="请输入生产厂家" style="width: 260px" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生产日期" prop="manufactorDate">
                <el-date-picker clearable v-model="form.manufactorDate" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择生产日期" style="width: 260px">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商" prop="supplierCode">
                <el-select v-model="form.supplierCode" placeholder="请选择供应商" style="width: 260px" filterable clearable>
                  <el-option v-for="(item, index) in supplierList" :key="index"
                    :label="item.supplierCode + '-' + item.supplierName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="采购日期" prop="purchaseDate">
                <el-date-picker clearable v-model="form.purchaseDate" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择采购日期" style="width: 260px">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入库日期" prop="stockInDate">
                <el-date-picker clearable v-model="form.stockInDate" type="date" value-format="yyyy-MM-dd"
                  placeholder="请选择入库日期" style="width: 260px">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="Mac地址" prop="macAddress">
                <el-input v-model="form.macAddress" placeholder="请输入Mac地址" style="width: 260px" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="使用部门" prop="department">
                <treeselect v-model="form.department" :options="deptOptions" :show-count="true"
                  placeholder="请输入安装人员所属部门" style="width: 260px" @select="handleNodeClick" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人" prop="pic">
                <el-select v-model="form.pic" placeholder="请选择安装负责人" style="width: 260px" @change="handleUsers"
                  filterable clearable>
                  <el-option v-for="(item, index) in userList" :key="index" :label="item.userName"
                    :value="item.userId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="负责人手机号码" prop="picPhone">
                <el-input v-model="form.picPhone" placeholder="请输入负责人手机号码" style="width: 260px" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="使用地点" prop="address">
                <el-input v-model="form.address" placeholder="请输入使用地点" style="width: 260px" />
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="设备图片" prop="equipImage">
                <image-upload v-model="form.equipImage" :limit="1" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备二维码" prop="equipQr">
                <image-upload v-model="form.equipQr" :limit="1" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="设备状态" prop="equipStatus">
                <el-switch v-model="form.equipStatus" active-value="0" inactive-value="1">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import Empty from "@/components/Empty/index.vue"
import {
  listEquip,
  getEquip,
  delEquip,
  addEquip,
  updateEquip,
} from "@/api/base/equip";
import {
  deptTreeSelect,
  listUser,
  listAllUser,
  listfuze,
} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listSupplier } from "@/api/system/supplier";

export default {
  name: "Equip",
  components: { Treeselect, Empty },
  dicts: ["equip_unit", "sys_normal_disable"],
  data() {
    return {
      isTable: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      equipCodes: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备信息表格数据
      equipList: [],
      deptOptions: [], //部门树
      userList: [], //用户list
      //供应商下拉列表
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipCode: null,
        equipName: null,
        equipType: null,
        specification: null,
        equipUnit: null,
        equipWeight: null,
        equipSize: null,
        equipManufactor: null,
        manufactorDate: null,
        supplierCode: null,
        purchaseDate: null,
        stockInDate: null,
        department: null,
        pic: null,
        picPhone: null,
        address: null,
        equipImage: null,
        equipQr: null,
        equipStatus: null,
        macAddress: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipCode: [
          { required: true, trigger: "blur", message: "设备编码不能为空" },
        ],
        equipName: [
          { required: true, trigger: "blur", message: "设备名称不能为空" },
        ],
        macAddress: [
          { required: true, trigger: "blur", message: "Mac地址不能为空" },
        ],
        specification: [
          { required: true, trigger: "blur", message: "规格型号不能为空" },
        ],
        equipImage: [
          { required: true, trigger: "blur", message: "设备图片不能为空" },
        ],
        equipWeight: [
         { validator: this.validateNumber, trigger: "blur" },
        ],
        picPhone: [
          // { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getSupplier();
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
        console.log(this.deptOptions, "response");
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.userList = [];
      this.form.pic = null;
      const params = {
        deptId: data.id,
        pageNum: 1,
        pageSize: 1000,
        // userId:this.$store.getters.user.userId,
      };
      listUser(params).then((response) => {
        this.userList = response.rows;
        console.log(this.userList, "111111");
      });
    },
    //用户点击事件
    handleUsers(data) {
      console.log(data, "用户");
      let obj = {};
      obj = this.userList.find((item) => {
        return item.userId === data;
      });
      this.form.picPhone = obj.phonenumber;
    },
    //获取供应商下拉列表
    getSupplier() {
      const params = {
        pageNum: 1,
        pageSize: 1000,
      };
      listSupplier(params).then((response) => {
        this.supplierList = response.rows;
      });
    },
    /** 查询设备信息列表 */
    getList() {
      this.loading = true;
      listEquip(this.queryParams).then((response) => {
        this.equipList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        equipCode: null,
        equipName: null,
        equipType: null,
        specification: null,
        equipUnit: null,
        equipWeight: null,
        equipSize: null,
        equipManufactor: null,
        manufactorDate: null,
        supplierCode: null,
        purchaseDate: null,
        stockInDate: null,
        department: null,
        pic: null,
        picPhone: null,
        address: null,
        equipImage: null,
        equipQr: null,
        equipStatus: "0",
        macAddress: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log("11111");

      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.equipCodes = selection.map((item) => item.equipCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEquip(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备信息";
      });
    },
        // 自定义校验规则：检查是否为数字
    validateNumber(rule, value, callback) {
      if (value === null || value === undefined || value === "") {
        callback(); // 允许为空
      } else if (!/^\d+(\.\d+)?$/.test(value)) {
        callback(new Error("请输入有效数字")); // 非数字时提示错误
      } else {
        callback(); // 校验通过
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEquip(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEquip(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const equipCodes = row.equipCode || this.equipCodes;
      this.$modal
        .confirm('是否确认删除设备信息编号为"' + equipCodes + '"的数据项？')
        .then(function () {
          return delEquip(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/equip/export",
        {
          ...this.queryParams,
        },
        `equip_${new Date().getTime()}.xlsx`
      );
    },
    // 设备状态修改
    handleStatusChange(row) {
      console.log(row, "12312312");
      let text = row.equipStatus === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.equipCode + '"设备吗？')
        .then(function () {
          return updateEquip(row);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.equipStatus = row.equipStatus === "0" ? "1" : "0";
        });
    },
    //切换列表展示方式
    toggleList() {
      this.isTable = !this.isTable;
    },
  },
};
</script>
<style lang="scss" scoped>
.ul-height {
  height: 62vh;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  margin-right: 3%;
  overflow-y: scroll;
  overflow-x: hidden;

  .colClass {
    height: 48%;
    margin-bottom: 20px
  }

  ::v-deep .el-card {
    height: 450px;
  }

  .imgClass {
    width: 500px;
    // height: 100%;
    display: block;
    height: 200px;
    background: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    ::v-deep .el-image__inner {
      height: 100%;
      width: 60%;
    }
  }

  .contentTitle {
    font-weight: 500;
    font-size: 18px;
    margin-left: 0;
    margin-bottom: 3px;
  }

  .contentSpan {
    font-size: 15px;
    margin-left: 0;
  }

  li {
    border: 1px solid #dcdfe6;
    width: 15%;
    height: 410px;
    color: #1c1b1b;
    float: left;
    list-style: none;
    margin: 5px;
    cursor: pointer;
    overflow: hidden;
  }
}

.emptyClass {
  background-color: #fff;
  border-radius: 20px;
  width: 300px;
  height: 350px;
  margin: auto;
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
}
</style>