import request from '@/utils/request'

// 查询检验模板列表
export function listTemplate(query) {
  return request({
    url: '/quality/template/list',
    method: 'get',
    params: query
  })
}

// 查询检验模板详细
export function getTemplate(id) {
  return request({
    url: '/quality/template/' + id,
    method: 'get'
  })
}

export function getTemplateByClass(query) {
  return request({
    url: '/quality/item/class',
    method: 'get',
    params: query
  })
}

// 新增检验模板
export function addTemplate(data) {
  return request({
    url: '/quality/template',
    method: 'post',
    data: data
  })
}

// 修改检验模板
export function updateTemplate(data) {
  return request({
    url: '/quality/template',
    method: 'put',
    data: data
  })
}

// 删除检验模板
export function delTemplate(id) {
  return request({
    url: '/quality/template/' + id,
    method: 'delete'
  })
}
