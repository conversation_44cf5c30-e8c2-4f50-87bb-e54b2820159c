import request from '@/utils/request'

// 查询收货单列表
export function listReceive(query) {
  return request({
    url: '/system/receive/list',
    method: 'get',
    params: query
  })
}

// 查询收货单详细
export function getReceive(id) {
  return request({
    url: '/system/receive/' + id,
    method: 'get'
  })
}

// 新增收货单
export function addReceive(data) {
  return request({
    url: '/system/receive',
    method: 'post',
    data: data
  })
}

// 修改收货单
export function updateReceive(data) {
  return request({
    url: '/system/receive',
    method: 'put',
    data: data
  })
}

// 删除收货单
export function delReceive(id) {
  return request({
    url: '/system/receive/' + id,
    method: 'delete'
  })
}
