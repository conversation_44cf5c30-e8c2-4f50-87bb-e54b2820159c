<template>
    <div id="print-template" class="print-hide">
      <div v-for="(item, index) in newData" :key="index">
        <div style="width:20px;height: 30px;"></div>
        <div>
          <div style="display: inline-block">品名：{{ item.name }}</div>
          <div style="display: inline-block">RFID:{{ item.name }}</div>
        </div>
        <div>
          <!-- <img :id="'jsbarcodeImg' + index" style="width: 220px;" /> -->
          <!-- <qrcode-vue
            :value="item?.num?.toString()"
            :size="120"
            :margin="5"
          /> -->
        </div>
      </div>
    </div>
  </template>
  <script>
  import printJS from "print-js";
//   import QrcodeVue from "qrcode.vue";
  import JsBarcode from "jsbarcode";
   
  export default {
    components: {
    //   QrcodeVue,
    },
    data() {
      return {
        newData: [
           {id:1, name:"乙醇", num: 12345678},
           {id:2, name:"葡萄糖", num: 876543},         
        ],
      };
    },
    mounted() {
    //   this.getBarcode();
      setTimeout(() => {
        this.handlePrint();
      }, 500);
      // this.changeImg();
    },
    beforeUnmount(){
      clearTimeout()
    },
    methods: {
      handlePrint() {
        // 获取打印模板的DOM元素
        const printElement = document.getElementById("print-template");
        console.log(printElement);
        if (printElement) {
          // 显示打印模板，以便能够打印
          printElement.classList.remove("print-hide");
          // 使用vue-printjs打印模板内容
          printJS({
            printable: printElement,
            type: "html",
            error: (err) => {
              console.log("err", err);
            },
          });
          // 打印完成后隐藏打印模板
          printElement.classList.add("print-hide");
        }
      },
      getBarcode() {
        this.newData.forEach((v, index) => {
          JsBarcode("#jsbarcodeImg" + index, v.num+'12345678', {
            format: "CODE39",
            width: 2,
            height: 80,
            marginRight:30,
            fontSize: 30,
            // displayValue: false,
          });
        });
      },
    },
  };
  </script>
  <style lang="scss">
  .print-hide {
    display: none; /* 在屏幕上隐藏打印模板 */
  }
  </style>