import request from '@/utils/request'

// 查询抽样方案列表
export function listScheme(query) {
  return request({
    url: '/quality/scheme/list',
    method: 'get',
    params: query
  })
}
export function listSchemeMenu(query) {
  return request({
    url: '/quality/scheme/listMenu',
    method: 'get',
    params: query
  })
}
// 查询抽样方案详细
export function getScheme(id) {
  return request({
    url: '/quality/scheme/' + id,
    method: 'get'
  })
}

// 新增抽样方案
export function addScheme(data) {
  return request({
    url: '/quality/scheme',
    method: 'post',
    data: data
  })
}

// 修改抽样方案
export function updateScheme(data) {
  return request({
    url: '/quality/scheme',
    method: 'put',
    data: data
  })
}

// 删除抽样方案
export function delScheme(id) {
  return request({
    url: '/quality/scheme/' + id,
    method: 'delete'
  })
}
