<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="收货单号" prop="receiveNo">
        <el-input
          v-model="queryParams.receiveNo"
          placeholder="请输入收货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货单id" prop="receiveId">
        <el-input
          v-model="queryParams.receiveId"
          placeholder="请输入收货单id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购单号" prop="purchaseNo">
        <el-input
          v-model="queryParams.purchaseNo"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购单号" prop="purchaseNo">
        <el-input
          v-model="queryParams.purchaseNo"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购单明细id" prop="purchaseDetailId">
        <el-input
          v-model="queryParams.purchaseDetailId"
          placeholder="请输入采购单明细id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格型号" prop="specification">
        <el-input
          v-model="queryParams.specification"
          placeholder="请输入规格型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单位" prop="materialUnit">
        <el-input
          v-model="queryParams.materialUnit"
          placeholder="请输入单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数量-总数" prop="qty">
        <el-input
          v-model="queryParams.qty"
          placeholder="请输入数量-总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行号" prop="receiveLine">
        <el-input
          v-model="queryParams.receiveLine"
          placeholder="请输入行号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已收数量" prop="receiveQty">
        <el-input
          v-model="queryParams.receiveQty"
          placeholder="请输入已收数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="批次" prop="batchNo">
        <el-input
          v-model="queryParams.batchNo"
          placeholder="请输入批次"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:receiveDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:receiveDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:receiveDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:receiveDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="receiveDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="收货单号" align="center" prop="receiveNo" />
      <el-table-column label="收货单id" align="center" prop="receiveId" />
      <el-table-column label="采购单号" align="center" prop="purchaseNo" />
      <el-table-column label="采购单号" align="center" prop="purchaseNo" />
      <el-table-column label="采购单明细id" align="center" prop="purchaseDetailId" />
      <el-table-column label="物料id" align="center" prop="materialId" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="规格型号" align="center" prop="specification" />
      <el-table-column label="单位" align="center" prop="materialUnit" />
      <el-table-column label="数量-总数" align="center" prop="qty" />
      <el-table-column label="行号" align="center" prop="receiveLine" />
      <el-table-column label="已收数量" align="center" prop="receiveQty" />
      <el-table-column label="批次" align="center" prop="batchNo" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:receiveDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:receiveDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收货单明细对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="收货单号" prop="receiveNo" style="width: 240px;">
          <el-input v-model="form.receiveNo" placeholder="请输入收货单号" />
        </el-form-item>
        <el-form-item label="收货单id" prop="receiveId" style="width: 240px;">
          <el-input v-model="form.receiveId" placeholder="请输入收货单id" />
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo" style="width: 240px;">
          <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo" style="width: 240px;">
          <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
        </el-form-item>
        <el-form-item label="采购单明细id" prop="purchaseDetailId" style="width: 240px;">
          <el-input v-model="form.purchaseDetailId" placeholder="请输入采购单明细id" />
        </el-form-item>
        <el-form-item label="物料id" prop="materialId" style="width: 240px;">
          <el-input v-model="form.materialId" placeholder="请输入物料id" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode" style="width: 240px;">
          <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName" style="width: 240px;">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification" style="width: 240px;">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="单位" prop="materialUnit" style="width: 240px;">
          <el-input v-model="form.materialUnit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="数量-总数" prop="qty" style="width: 240px;">
          <el-input v-model="form.qty" placeholder="请输入数量-总数" />
        </el-form-item>
        <el-form-item label="行号" prop="receiveLine" style="width: 240px;">
          <el-input v-model="form.receiveLine" placeholder="请输入行号" />
        </el-form-item>
        <el-form-item label="已收数量" prop="receiveQty" style="width: 240px;">
          <el-input v-model="form.receiveQty" placeholder="请输入已收数量" />
        </el-form-item>
        <el-form-item label="批次" prop="batchNo" style="width: 240px;">
          <el-input v-model="form.batchNo" placeholder="请输入批次" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-divider content-position="center">收货单箱信息信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddWmsReceiveBox">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteWmsReceiveBox">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="wmsReceiveBoxList" :row-class-name="rowWmsReceiveBoxIndex" @selection-change="handleWmsReceiveBoxSelectionChange" ref="wmsReceiveBox">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="收货主表id" prop="receiveId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.receiveId" placeholder="请输入收货主表id" />
            </template>
          </el-table-column>
          <el-table-column label="收货单号" prop="receiveNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.receiveNo" placeholder="请输入收货单号" />
            </template>
          </el-table-column>
          <el-table-column label="物料id" prop="materialId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialId" placeholder="请输入物料id" />
            </template>
          </el-table-column>
          <el-table-column label="物料编码" prop="materialCode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialCode" placeholder="请输入物料编码" />
            </template>
          </el-table-column>
          <el-table-column label="物料名称" prop="materialName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" />
            </template>
          </el-table-column>
          <el-table-column label="规格型号" prop="specification" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.specification" placeholder="请输入规格型号" />
            </template>
          </el-table-column>
          <el-table-column label="单位" prop="materialUnit" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialUnit" placeholder="请输入单位" />
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="qty" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" placeholder="请输入数量" />
            </template>
          </el-table-column>
          <el-table-column label="箱号" prop="boxNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.boxNo" placeholder="请输入箱号" />
            </template>
          </el-table-column>
          <el-table-column label="批次" prop="batchNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.batchNo" placeholder="请输入批次" />
            </template>
          </el-table-column>
          <el-table-column label="是否暂存；" prop="isStaging" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.isStaging" placeholder="请输入是否暂存；" />
            </template>
          </el-table-column>
          <el-table-column label="组织" prop="comId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comId" placeholder="请输入组织" />
            </template>
          </el-table-column>
          <el-table-column label="生产日期" prop="dateCode" width="240">
            <template slot-scope="scope">
              <el-date-picker clearable v-model="scope.row.dateCode" type="date" value-format="yyyy-MM-dd" placeholder="请选择生产日期" />
            </template>
          </el-table-column>
          <el-table-column label="保质期" prop="expirationDate" width="240">
            <template slot-scope="scope">
              <el-date-picker clearable v-model="scope.row.expirationDate" type="date" value-format="yyyy-MM-dd" placeholder="请选择保质期" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listReceiveDetail, getReceiveDetail, delReceiveDetail, addReceiveDetail, updateReceiveDetail } from "@/api/system/receiveDetail";

export default {
  name: "ReceiveDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsReceiveBox: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货单明细表格数据
      receiveDetailList: [],
      // 收货单箱信息表格数据
      wmsReceiveBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        receiveNo: null,
        receiveId: null,
        purchaseNo: null,
        purchaseNo: null,
        purchaseDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        receiveLine: null,
        receiveQty: null,
        batchNo: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        qty: [
          { required: true, message: "数量-总数不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询收货单明细列表 */
    getList() {
      this.loading = true;
      listReceiveDetail(this.queryParams).then(response => {
        this.receiveDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        receiveNo: null,
        receiveId: null,
        purchaseNo: null,
        purchaseNo: null,
        purchaseDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        receiveLine: null,
        receiveQty: null,
        batchNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.wmsReceiveBoxList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加收货单明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getReceiveDetail(id).then(response => {
        this.form = response.data;
        this.wmsReceiveBoxList = response.data.wmsReceiveBoxList;
        this.open = true;
        this.title = "修改收货单明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.wmsReceiveBoxList = this.wmsReceiveBoxList;
          if (this.form.id != null) {
            updateReceiveDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReceiveDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除收货单明细编号为"' + ids + '"的数据项？').then(function() {
        return delReceiveDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
	/** 收货单箱信息序号 */
    rowWmsReceiveBoxIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 收货单箱信息添加按钮操作 */
    handleAddWmsReceiveBox() {
      let obj = {};
      obj.receiveId = "";
      obj.receiveNo = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.boxNo = "";
      obj.qrCode = "";
      obj.batchNo = "";
      obj.isStaging = "";
      obj.remark = "";
      obj.comId = "";
      obj.dateCode = "";
      obj.expirationDate = "";
      this.wmsReceiveBoxList.push(obj);
    },
    /** 收货单箱信息删除按钮操作 */
    handleDeleteWmsReceiveBox() {
      if (this.checkedWmsReceiveBox.length == 0) {
        this.$modal.msgError("请先选择要删除的收货单箱信息数据");
      } else {
        const wmsReceiveBoxList = this.wmsReceiveBoxList;
        const checkedWmsReceiveBox = this.checkedWmsReceiveBox;
        this.wmsReceiveBoxList = wmsReceiveBoxList.filter(function(item) {
          return checkedWmsReceiveBox.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsReceiveBoxSelectionChange(selection) {
      this.checkedWmsReceiveBox = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/receiveDetail/export', {
        ...this.queryParams
      }, `收货单明细_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
