<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="样本数量" prop="sampleNum">
        <el-input
          v-model="queryParams.sampleNum"
          placeholder="请输入样本数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="字母表" prop="capital">
        <el-input
          v-model="queryParams.capital"
          placeholder="请输入字母表"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
拒收数" prop="rejectNum">
        <el-input
          v-model="queryParams.rejectNum"
          placeholder="请输入	
拒收数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="允收数" prop="acceptNum">
        <el-input
          v-model="queryParams.acceptNum"
          placeholder="请输入允收数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
起始批量" prop="startNum">
        <el-input
          v-model="queryParams.startNum"
          placeholder="请输入	
起始批量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
截止批量" prop="endNum">
        <el-input
          v-model="queryParams.endNum"
          placeholder="请输入	
截止批量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数量" prop="number">
        <el-input
          v-model="queryParams.number"
          placeholder="请输入数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="抽样主表id" prop="sampleId">
        <el-input
          v-model="queryParams.sampleId"
          placeholder="请输入抽样主表id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:method:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quality:method:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quality:method:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:method:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="methodList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="样本数量" align="center" prop="sampleNum" />
      <el-table-column label="字母表" align="center" prop="capital" />
      <el-table-column label="	
拒收数" align="center" prop="rejectNum" />
      <el-table-column label="允收数" align="center" prop="acceptNum" />
      <el-table-column label="	
起始批量" align="center" prop="startNum" />
      <el-table-column label="	
截止批量" align="center" prop="endNum" />
      <el-table-column label="数量" align="center" prop="number" />
      <el-table-column label="抽样主表id" align="center" prop="sampleId" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:method:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:method:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改抽样方法对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="样本数量" prop="sampleNum" style="width: 240px;">
          <el-input v-model="form.sampleNum" placeholder="请输入样本数量" />
        </el-form-item>
        <el-form-item label="字母表" prop="capital" style="width: 240px;">
          <el-input v-model="form.capital" placeholder="请输入字母表" />
        </el-form-item>
        <el-form-item label="	
拒收数" prop="rejectNum" style="width: 240px;">
          <el-input v-model="form.rejectNum" placeholder="请输入	
拒收数" />
        </el-form-item>
        <el-form-item label="允收数" prop="acceptNum" style="width: 240px;">
          <el-input v-model="form.acceptNum" placeholder="请输入允收数" />
        </el-form-item>
        <el-form-item label="	
起始批量" prop="startNum" style="width: 240px;">
          <el-input v-model="form.startNum" placeholder="请输入	
起始批量" />
        </el-form-item>
        <el-form-item label="	
截止批量" prop="endNum" style="width: 240px;">
          <el-input v-model="form.endNum" placeholder="请输入	
截止批量" />
        </el-form-item>
        <el-form-item label="数量" prop="number" style="width: 240px;">
          <el-input v-model="form.number" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="抽样主表id" prop="sampleId" style="width: 240px;">
          <el-input v-model="form.sampleId" placeholder="请输入抽样主表id" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listMethod, getMethod, delMethod, addMethod, updateMethod } from "@/api/quality/method";

export default {
  name: "Method",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 抽样方法表格数据
      methodList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sampleNum: null,
        capital: null,
        rejectNum: null,
        acceptNum: null,
        startNum: null,
        endNum: null,
        number: null,
        sampleId: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询抽样方法列表 */
    getList() {
      this.loading = true;
      listMethod(this.queryParams).then(response => {
        this.methodList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sampleNum: null,
        capital: null,
        rejectNum: null,
        acceptNum: null,
        startNum: null,
        endNum: null,
        number: null,
        sampleId: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加抽样方法";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMethod(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改抽样方法";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMethod(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMethod(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除抽样方法编号为"' + ids + '"的数据项？').then(function() {
        return delMethod(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('quality/method/export', {
        ...this.queryParams
      }, `抽样方法_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
