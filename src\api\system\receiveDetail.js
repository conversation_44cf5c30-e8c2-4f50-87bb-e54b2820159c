import request from '@/utils/request'

// 查询收货单明细列表
export function listReceiveDetail(query) {
  return request({
    url: '/system/receiveDetail/list',
    method: 'get',
    params: query
  })
}

// 查询收货单明细详细
export function getReceiveDetail(id) {
  return request({
    url: '/system/receiveDetail/' + id,
    method: 'get'
  })
}

// 新增收货单明细
export function addReceiveDetail(data) {
  return request({
    url: '/system/receiveDetail',
    method: 'post',
    data: data
  })
}

// 修改收货单明细
export function updateReceiveDetail(data) {
  return request({
    url: '/system/receiveDetail',
    method: 'put',
    data: data
  })
}

// 删除收货单明细
export function delReceiveDetail(id) {
  return request({
    url: '/system/receiveDetail/' + id,
    method: 'delete'
  })
}
