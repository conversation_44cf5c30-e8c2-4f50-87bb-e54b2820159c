import request from '@/utils/request'

// 查询库存流水列表
export function listTransaction(query) {
  return request({
    url: '/system/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询库存流水详细
export function getTransaction(id) {
  return request({
    url: '/system/transaction/' + id,
    method: 'get'
  })
}

// 新增库存流水
export function addTransaction(data) {
  return request({
    url: '/system/transaction',
    method: 'post',
    data: data
  })
}

// 修改库存流水
export function updateTransaction(data) {
  return request({
    url: '/system/transaction',
    method: 'put',
    data: data
  })
}

// 删除库存流水
export function delTransaction(id) {
  return request({
    url: '/system/transaction/' + id,
    method: 'delete'
  })
}
