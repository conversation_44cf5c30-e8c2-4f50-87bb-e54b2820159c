import request from '@/utils/request'

// 查询库位列表
export function listWarehouse_location(query) {
  return request({
    url: '/system/warehouse_location/list',
    method: 'get',
    params: query
  })
}

// 查询库位详细
export function getWarehouse_location(id) {
  return request({
    url: '/system/warehouse_location/' + id,
    method: 'get'
  })
}

// 新增库位
export function addWarehouse_location(data) {
  return request({
    url: '/system/warehouse_location',
    method: 'post',
    data: data
  })
}

// 修改库位
export function updateWarehouse_location(data) {
  return request({
    url: '/system/warehouse_location',
    method: 'put',
    data: data
  })
}

// 删除库位
export function delWarehouse_location(id) {
  return request({
    url: '/system/warehouse_location/' + id,
    method: 'delete'
  })
}
