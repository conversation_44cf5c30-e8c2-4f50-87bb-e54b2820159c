<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="编码" prop="binTypeCode">
          <el-input v-model="queryParams.binTypeCode" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="名称" prop="binTypeName">
          <el-input v-model="queryParams.binTypeName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:binType:add']">新增</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="binTypeList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="编码" align="center" prop="binTypeCode" />
        <el-table-column label="名称" align="center" prop="binTypeName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:binType:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:binType:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改库位类型对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'25%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="库位类型信息" name="1">
              <el-form-item label="编码" prop="binTypeCode">
                <el-input v-model="form.binTypeCode" placeholder="请输入编码" />
              </el-form-item>
              <el-form-item label="名称" prop="binTypeName">
                <el-input v-model="form.binTypeName" placeholder="请输入名称" />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>

        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { listBinType, getBinType, delBinType, addBinType, updateBinType } from "@/api/system/binType";

export default {
  name: "BinType",
  data() {
    return {
      activeNames: ["1"],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库位类型表格数据
      binTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        binTypeCode: null,
        binTypeName: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        binTypeCode: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ],
        binTypeName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询库位类型列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      listBinType(this.queryParams).then(response => {
        this.binTypeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        binTypeCode: null,
        binTypeName: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加库位类型";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getBinType(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库位类型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateBinType(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBinType(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const code = row.binTypeCode;
      this.$modal.confirm('是否确认删除编码为"' + code + '"数据项？').then(function () {
        return delBinType(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/binType/export', {
        ...this.queryParams
      }, `binType_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
