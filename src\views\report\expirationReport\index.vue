<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="100px"
      >
        <el-form-item label="物料编码/名称" prop="searchMaterialKeyword">
          <el-input
            v-model="queryParams.searchMaterialKeyword"
            placeholder="请输入物料编码或名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库编码/名称" prop="searchWarehouseKeyword">
          <el-input
            v-model="queryParams.searchWarehouseKeyword"
            placeholder="请输入仓库编码或名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="inventoryList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
      <el-table-column type="index" width="50" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="总数" align="center" prop="qty" />
        <!-- <el-table-column label="安全库存" align="center" prop="safetyStock" />
        <el-table-column label="安全库存预警" align="center"/> -->
        <el-table-column label="低于预警时间" align="center" prop="belowWarningTime" />
        <el-table-column label="保质期预警天数" align="center" prop="expirationWarningDays" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 废弃的弹窗代码
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        ...
      </el-drawer> -->
      
    </div>
  </div>
</template>

<script>
import { listInventory } from "@/api/system/inventory";

export default {
  name: "safetyReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // wms库存表格数据
      inventoryList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        comId: null,
        palletNo: null,
        palletQrCode: null,
        // 物料编码和名称
        searchMaterialKeyword: null,
        // 库区编码和名称
        searchAreaKeyword: null,
        // 仓库编码和名称
        searchWarehouseKeyword: null,
        // 库位编码和名称
        searchLocationKeyword: null,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询wms库存列表 */
    getList() {
      this.loading = true;
      listInventory(this.queryParams).then((response) => {
          // this.inventoryList = this.sortArrayByField(response.rows, 'createTime');
        this.inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  height: 100%;
  box-sizing: border-box;
}

.app-container-div {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  .el-form {
    margin-bottom: 0px !important;
  }
}
</style>
