import request from '@/utils/request'

// 查询出库单箱信息列表
export function listStockOutBox(query) {
  return request({
    url: '/system/stockOutBox/list',
    method: 'get',
    params: query
  })
}

// 查询出库单箱信息详细
export function getStockOutBox(id) {
  return request({
    url: '/system/stockOutBox/' + id,
    method: 'get'
  })
}

// 新增出库单箱信息
export function addStockOutBox(data) {
  return request({
    url: '/system/stockOutBox',
    method: 'post',
    data: data
  })
}

// 修改出库单箱信息
export function updateStockOutBox(data) {
  return request({
    url: '/system/stockOutBox',
    method: 'put',
    data: data
  })
}

// 删除出库单箱信息
export function delStockOutBox(id) {
  return request({
    url: '/system/stockOutBox/' + id,
    method: 'delete'
  })
}
