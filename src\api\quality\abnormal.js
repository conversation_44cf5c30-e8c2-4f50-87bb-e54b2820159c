import request from '@/utils/request'

// 查询单据异常反馈单列表
export function listAbnormal(query) {
  return request({
    url: '/quality/abnormal/list',
    method: 'get',
    params: query
  })
}

// 查询单据异常反馈单详细
export function getAbnormal(id) {
  return request({
    url: '/quality/abnormal/' + id,
    method: 'get'
  })
}

// 新增单据异常反馈单
export function addAbnormal(data) {
  return request({
    url: '/quality/abnormal',
    method: 'post',
    data: data
  })
}

// 修改单据异常反馈单
export function updateAbnormal(data) {
  return request({
    url: '/quality/abnormal',
    method: 'put',
    data: data
  })
}

// 删除单据异常反馈单
export function delAbnormal(id) {
  return request({
    url: '/quality/abnormal/' + id,
    method: 'delete'
  })
}

// 单据异常反馈单挑选实现
export function delAbnormal(id) {
  return request({
    url: '/quality/abnormal/' + id,
    method: 'delete'
  })
}
