<template>
    <el-table height="62vh" v-loading="loading" :data="moveBoxList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <!-- <el-table-column label="移库主表id" align="center" prop="moveId" /> -->
      <el-table-column label="移库单号" align="center" prop="moveNo" />
      <!-- <el-table-column label="移库明细id" align="center" prop="moveDetailId" /> -->
      <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="规格型号" align="center" prop="specification" />
      <!-- <el-table-column label="单位" align="center" prop="materialUnit" /> -->
      <el-table-column label="数量" align="center" prop="qty" />
      <el-table-column label="箱号" align="center" prop="boxNo" />
      <el-table-column label="箱二维码" align="center" prop="qrCode" />
      <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
      <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
      <el-table-column label="库区编码" align="center" prop="areaCode" />
      <el-table-column label="库区名称" align="center" prop="areaName" />
      <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
      <el-table-column label="库位编码" align="center" prop="locationCode" />
      <el-table-column label="库位名称" align="center" prop="locationName" />
      <el-table-column label="批次" align="center" prop="batchNo" />
      <el-table-column label="是否暂存；" align="center" prop="isStaging" />
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
      <el-table-column label="生产日期" align="center" prop="dateCode" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCode, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保质期" align="center" prop="expirationDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="供应商id" align="center" prop="supplierId" /> -->
      <!-- <el-table-column label="供应商编码" align="center" prop="supplierCode" />
      <el-table-column label="供应商名称" align="center" prop="supplierName" /> -->
      <!-- <el-table-column label="目标仓库id" align="center" prop="targetWarehouseId" /> -->
      <el-table-column label="目标仓库编码" align="center" prop="targetWarehouseCode" />
      <el-table-column label="目标仓库名称" align="center" prop="targetWarehouseName" />
      <!-- <el-table-column label="目标库区id" align="center" prop="targetAreaId" /> -->
      <el-table-column label="目标库区编码" align="center" prop="targetAreaCode" />
      <el-table-column label="目标库区名称" align="center" prop="targetAreaName" />
      <!-- <el-table-column label="目标库位id" align="center" prop="targetLocationId" /> -->
      <el-table-column label="目标库位编码" align="center" prop="targetLocationCode" />
      <el-table-column label="目标库位名称" align="center" prop="targetLocationName" />
      <!-- <el-table-column label="移库状态" align="center" prop="moveState" /> -->
    </el-table>
</template>

<script>
import { listMoveBox, getMoveBox, delMoveBox, addMoveBox, updateMoveBox } from "@/api/system/moveBox";

export default {
  name: "MoveBox",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 移库箱信息表格数据
      moveBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moveId: null,
        moveNo: null,
        moveDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        comId: null,
        dateCode: null,
        expirationDate: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        targetWarehouseId: null,
        targetWarehouseCode: null,
        targetWarehouseName: null,
        targetAreaId: null,
        targetAreaCode: null,
        targetAreaName: null,
        targetLocationId: null,
        targetLocationCode: null,
        targetLocationName: null,
        moveState: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        moveId: [
          { required: true, message: "移库主表id不能为空", trigger: "blur" }
        ],
        moveNo: [
          { required: true, message: "移库单号不能为空", trigger: "blur" }
        ],
        moveDetailId: [
          { required: true, message: "移库明细id不能为空", trigger: "blur" }
        ],
        boxNo: [
          { required: true, message: "箱号不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询移库箱信息列表 */
    getList() {
      this.loading = true;
      listMoveBox(this.queryParams).then(response => {
        this.moveBoxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        moveId: null,
        moveNo: null,
        moveDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        dateCode: null,
        expirationDate: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        targetWarehouseId: null,
        targetWarehouseCode: null,
        targetWarehouseName: null,
        targetAreaId: null,
        targetAreaCode: null,
        targetAreaName: null,
        targetLocationId: null,
        targetLocationCode: null,
        targetLocationName: null,
        moveState: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加移库箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMoveBox(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改移库箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMoveBox(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMoveBox(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除移库箱信息编号为"' + ids + '"的数据项？').then(function() {
        return delMoveBox(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/moveBox/export', {
        ...this.queryParams
      }, `移库箱信息_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
