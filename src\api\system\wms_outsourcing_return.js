import request from '@/utils/request'

// 查询委外退货列表
export function listWms_outsourcing_return(query) {
  return request({
    url: '/system/wms_outsourcing_return/list',
    method: 'get',
    params: query
  })
}

// 查询委外退货详细
export function getWms_outsourcing_return(id) {
  return request({
    url: '/system/wms_outsourcing_return/' + id,
    method: 'get'
  })
}

// 新增委外退货
export function addWms_outsourcing_return(data) {
  return request({
    url: '/system/wms_outsourcing_return',
    method: 'post',
    data: data
  })
}

// 修改委外退货
export function updateWms_outsourcing_return(data) {
  return request({
    url: '/system/wms_outsourcing_return',
    method: 'put',
    data: data
  })
}

// 删除委外退货
export function delWms_outsourcing_return(id) {
  return request({
    url: '/system/wms_outsourcing_return/' + id,
    method: 'delete'
  })
}
