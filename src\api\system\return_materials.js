import request from '@/utils/request'

// 查询生产退料列表
export function listReturn_materials(query) {
  return request({
    url: '/system/return_materials/list',
    method: 'get',
    params: query
  })
}

// 查询生产退料详细
export function getReturn_materials(id) {
  return request({
    url: '/system/return_materials/' + id,
    method: 'get'
  })
}

// 新增生产退料
export function addReturn_materials(data) {
  return request({
    url: '/system/return_materials',
    method: 'post',
    data: data
  })
}

// 修改生产退料
export function updateReturn_materials(data) {
  return request({
    url: '/system/return_materials',
    method: 'put',
    data: data
  })
}

// 删除生产退料
export function delReturn_materials(id) {
  return request({
    url: '/system/return_materials/' + id,
    method: 'delete'
  })
}
