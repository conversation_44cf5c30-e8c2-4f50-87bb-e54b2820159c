import request from '@/utils/request'

// 查询检验类型列表
export function listClass(query) {
  return request({
    url: '/quality/class/list',
    method: 'get',
    params: query
  })
}
export function listClassMenu(query) {
  return request({
    url: '/quality/class/listMenu',
    method: 'get',
    params: query
  })
}

// 查询检验类型详细
export function getClass(id) {
  return request({
    url: '/quality/class/' + id,
    method: 'get'
  })
}

// 新增检验类型
export function addClass(data) {
  return request({
    url: '/quality/class',
    method: 'post',
    data: data
  })
}

// 修改检验类型
export function updateClass(data) {
  return request({
    url: '/quality/class',
    method: 'put',
    data: data
  })
}

// 删除检验类型
export function delClass(id) {
  return request({
    url: '/quality/class/' + id,
    method: 'delete'
  })
}
