<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检验方法" prop="inspectionMethod">
        <el-input
          v-model="queryParams.inspectionMethod"
          placeholder="请输入检验方法"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验项目编号" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入检验项目编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验项目名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入检验项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
检验类型编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入	
检验类型编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
检验类型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入	
检验类型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="	
标准值" prop="standardValue">
        <el-input
          v-model="queryParams.standardValue"
          placeholder="请输入	
标准值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上限值" prop="upperLimit">
        <el-input
          v-model="queryParams.upperLimit"
          placeholder="请输入上限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下限值" prop="lowerLimit">
        <el-input
          v-model="queryParams.lowerLimit"
          placeholder="请输入下限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="技术标准" prop="technicalStandard">
        <el-input
          v-model="queryParams.technicalStandard"
          placeholder="请输入技术标准"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组内顺序" prop="groupSerial">
        <el-input
          v-model="queryParams.groupSerial"
          placeholder="请输入组内顺序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验顺序" prop="inspectionSerial">
        <el-input
          v-model="queryParams.inspectionSerial"
          placeholder="请输入检验顺序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检验分组" prop="inspectionGroup">
        <el-input
          v-model="queryParams.inspectionGroup"
          placeholder="请输入检验分组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['quality:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['quality:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['quality:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['quality:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="检验方法" align="center" prop="inspectionMethod" />
      <el-table-column label="检验类型id" align="center" prop="inspectionClassType" />
      <el-table-column label="检验项目id" align="center" prop="inspectionItemType" />
      <el-table-column label="检验项目编号" align="center" prop="itemCode" />
      <el-table-column label="检验项目名称" align="center" prop="itemName" />
      <el-table-column label="	
检验类型编码" align="center" prop="code" />
      <el-table-column label="	
检验类型名称" align="center" prop="name" />
      <el-table-column label="	
标准值" align="center" prop="standardValue" />
      <el-table-column label="上限值" align="center" prop="upperLimit" />
      <el-table-column label="下限值" align="center" prop="lowerLimit" />
      <el-table-column label="技术标准" align="center" prop="technicalStandard" />
      <el-table-column label="组内顺序" align="center" prop="groupSerial" />
      <el-table-column label="检验顺序" align="center" prop="inspectionSerial" />
      <el-table-column label="检验分组" align="center" prop="inspectionGroup" />
      <el-table-column label="模板类型" align="center" prop="inspectionTemplateType" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['quality:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['quality:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检验模板明细对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="检验方法" prop="inspectionMethod" style="width: 240px;">
          <el-input v-model="form.inspectionMethod" placeholder="请输入检验方法" />
        </el-form-item>
        <el-form-item label="检验项目编号" prop="itemCode" style="width: 240px;">
          <el-input v-model="form.itemCode" placeholder="请输入检验项目编号" />
        </el-form-item>
        <el-form-item label="检验项目名称" prop="itemName" style="width: 240px;">
          <el-input v-model="form.itemName" placeholder="请输入检验项目名称" />
        </el-form-item>
        <el-form-item label="	检验类型编码" prop="code" style="width: 240px;">
          <el-input v-model="form.code" placeholder="请输入	检验类型编码" />
        </el-form-item>
        <el-form-item label="	检验类型名称" prop="name" style="width: 240px;">
          <el-input v-model="form.name" placeholder="请输入	检验类型名称" />
        </el-form-item>
        <el-form-item label="	标准值" prop="standardValue" style="width: 240px;">
          <el-input v-model="form.standardValue" placeholder="请输入	标准值" />
        </el-form-item>
        <el-form-item label="上限值" prop="upperLimit" style="width: 240px;">
          <el-input v-model="form.upperLimit" placeholder="请输入上限值" />
        </el-form-item>
        <el-form-item label="下限值" prop="lowerLimit" style="width: 240px;">
          <el-input v-model="form.lowerLimit" placeholder="请输入下限值" />
        </el-form-item>
        <el-form-item label="技术标准" prop="technicalStandard" style="width: 240px;">
          <el-input v-model="form.technicalStandard" placeholder="请输入技术标准" />
        </el-form-item>
        <el-form-item label="组内顺序" prop="groupSerial" style="width: 240px;">
          <el-input v-model="form.groupSerial" placeholder="请输入组内顺序" />
        </el-form-item>
        <el-form-item label="检验顺序" prop="inspectionSerial" style="width: 240px;">
          <el-input v-model="form.inspectionSerial" placeholder="请输入检验顺序" />
        </el-form-item>
        <el-form-item label="检验分组" prop="inspectionGroup" style="width: 240px;">
          <el-input v-model="form.inspectionGroup" placeholder="请输入检验分组" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/quality/info";

export default {
  name: "Info",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检验模板明细表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inspectionMethod: null,
        inspectionClassType: null,
        inspectionItemType: null,
        itemCode: null,
        itemName: null,
        code: null,
        name: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        groupSerial: null,
        inspectionSerial: null,
        inspectionGroup: null,
        inspectionTemplateType: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询检验模板明细列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        inspectionMethod: null,
        inspectionClassType: null,
        inspectionItemType: null,
        itemCode: null,
        itemName: null,
        code: null,
        name: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        groupSerial: null,
        inspectionSerial: null,
        inspectionGroup: null,
        inspectionTemplateType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检验模板明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检验模板明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除检验模板明细编号为"' + ids + '"的数据项？').then(function() {
        return delInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('quality/info/export', {
        ...this.queryParams
      }, `检验模板明细_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
