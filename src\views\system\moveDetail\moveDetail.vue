<template>
  <div class="app-container">
      <div class="app-container-div">
       <el-form
        v-show="activeName == 'first'"
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
      <el-form-item label="移库单号" prop="moveNo">
        <el-input
          v-model="queryParams.moveNo"
          placeholder="请输入移库单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="移库状态" prop="moveState">
        <el-select v-model="queryParams.moveState" placeholder="请选择移库状态" clearable>
          <el-option
            v-for="dict in dict.type.move_state"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table 
            height="62vh" 
            v-loading="loading" 
            :data="moveDetailList" 
            @selection-change="handleSelectionChange"
            @row-click="sendDetailId"
            >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <el-table-column label="移库单号" align="center" prop="moveNo" />
      <!-- <el-table-column label="主表id" align="center" prop="moveId" /> -->
      <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
      <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="移库状态" align="center" prop="moveState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.move_state" :value="scope.row.moveState"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <!-- <el-table-column label="单位" align="center" prop="materialUnit" /> -->
      <el-table-column label="规格型号" align="center" prop="specification" />
      <el-table-column label="数量" align="center" prop="qty" />
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      
    />

      </div>
  </div>
</template>

<script>
import { listMoveDetail, getMoveDetail, delMoveDetail, addMoveDetail, updateMoveDetail } from "@/api/system/moveDetail";

export default {
  name: "MoveDetail",
  dicts: ['move_state'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 移库单明细表格数据
      moveDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moveNo: null,
        moveId: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        moveState: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialUnit: null,
        specification: null,
        qty: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" }
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        qty: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询移库单明细列表 */
    getList() {
      this.loading = true;
      listMoveDetail(this.queryParams).then(response => {
        this.moveDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        moveNo: null,
        moveId: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        moveState: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialUnit: null,
        specification: null,
        qty: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    sendDetailId(row, colom, env) {
      this.$emit("sendDetailId", row.id);
      console.log("row");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加移库单明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMoveDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改移库单明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMoveDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMoveDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除移库单明细编号为"' + ids + '"的数据项？').then(function() {
        return delMoveDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/moveDetail/export', {
        ...this.queryParams
      }, `移库单明细_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
