import request from '@/utils/request'

// 查询设备报修单列表
export function listBill(query) {
  return request({
    url: '/system/bill/list',
    method: 'get',
    params: query
  })
}

// 查询设备报修单详细
export function getBill(id) {
  return request({
    url: '/system/bill/' + id,
    method: 'get'
  })
}

// 新增设备报修单
export function addBill(data) {
  return request({
    url: '/system/bill',
    method: 'post',
    data: data
  })
}

// 修改设备报修单
export function updateBill(data) {
  return request({
    url: '/system/bill',
    method: 'put',
    data: data
  })
}

// 删除设备报修单
export function delBill(id) {
  return request({
    url: '/system/bill/' + id,
    method: 'delete'
  })
}
