<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="100px"
      >
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input
            v-model="queryParams.supplierCode"
            placeholder="请输入供应商编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="箱号" prop="boxNo" v-show="showMoreConditions">
          <el-input
            v-model="queryParams.boxNo"
            placeholder="请输入箱号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="父箱号"
          prop="parentBoxNo"
          v-show="showMoreConditions"
        >
          <el-input
            v-model="queryParams.parentBoxNo"
            placeholder="请输入父箱号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="状态"
          prop="boxState"
          v-show="showMoreConditions"
          @keyup.enter.native="handleQuery"
        >
          <el-select
            v-model="queryParams.boxState"
            placeholder="请选择出入库状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.box_state"
              :key="dict.id"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="批次" prop="batchNo" v-show="showMoreConditions">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批次"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="订单分类"
          prop="orderClass"
          v-show="showMoreConditions"
          @keyup.enter.native="handleQuery"
        >
          <el-select
            v-model="queryParams.orderClass"
            placeholder="请选择订单分类"
            clearable
          >
            <el-option
              v-for="dict in dict.type.order_class"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <span @click="toggleMoreConditions" class="moreClass"
            >{{ showMoreConditions ? "收起" : "更多条件"
            }}<i
              :class="
                !showMoreConditions ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
              "
            ></i
          ></span>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
          <!--  导入按钮-->

          <!-- <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleImport"
            >导入</el-button
          > -->
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:wmsBoxPeriodOut:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:wmsBoxPeriodOut:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:wmsBoxPeriodOut:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:wmsBoxPeriodOut:export']"
            >导出</el-button
          >
        </el-col>

       <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <!-- wmsBoxPeriodOutList -->
      <el-table
        height="62vh"
        v-loading="loading"
        :data="wmsBoxPeriodOutList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="箱号" align="center" prop="boxNo" :width="tableWidth(boxList.map(item=>{item.boxNo}))+130" /> -->
        <el-table-column
          label="采购单号"
          align="center"
          prop="purchaseNo"
          width="120"
        />

        <el-table-column
          label="箱号"
          align="center"
          prop="boxNo"
          :width="
            tableWidth(
              wmsBoxPeriodOutList.map((item) => {
                item.boxNo;
              })
            ) + 130
          "
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.boxNo"
              >
                <span class="ellipsis" style="display: inline-block">
                  {{ scope.row.boxNo }}</span
                >
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.boxNo"
                v-clipboard:success="onCopy"
              >
              </i>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="父箱号" align="center" prop="parentBoxNo" />
        <el-table-column
          label="供应商编码"
          align="center"
          prop="supplierCode"
          width="120"
        />
        <el-table-column
          label="供应商名称"
          align="center"
          prop="supplierName"
          width="120"
        />
        <el-table-column
          label="批次"
          align="center"
          prop="batchNo"
          width="130"
        />
        <el-table-column
          label="物料编码"
          align="center"
          prop="materialCode"
          width="120"
        />
        <el-table-column
          label="物料名称"
          align="center"
          prop="materialName"
          :width="
            tableWidth(
              wmsBoxPeriodOutList.map((item) => {
                item.materialName;
              })
            )
          "
        />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column
          label="生产日期"
          align="center"
          prop="dateCode"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="保质期"
          align="center"
          prop="expirationDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.expirationDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="boxState">
          <template slot-scope="scope">
            <dict-tag
              type="success"
              :options="dict.type.box_state"
              :value="scope.row.boxState"
            />
          </template>
        </el-table-column>
        <el-table-column label="订单分类" align="center" prop="orderClass">
          <template slot-scope="scope">
            <dict-tag
              type="success"
              :options="dict.type.order_class"
              :value="scope.row.orderClass"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          width="150"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handlePrint(scope.row)"
              >打印</el-button
            >
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:box:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:box:remove']"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改期出库存对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="箱号" prop="boxNo">
            <el-input v-model="form.boxNo" placeholder="请输入箱号" />
          </el-form-item>
          <el-form-item label="采购单号" prop="purchaseNo">
            <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
          </el-form-item>
          <!-- <el-form-item label="采购单号" prop="purchaseNo">
            <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
          </el-form-item> -->
          <!-- <el-form-item label="供应商id" prop="supplierId">
            <el-input v-model="form.supplierId" placeholder="请输入供应商id" />
          </el-form-item> -->
          <el-form-item label="供应商编码" prop="supplierCode">
            <el-input
              v-model="form.supplierCode"
              placeholder="请输入供应商编码"
            />
          </el-form-item>
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input
              v-model="form.supplierName"
              placeholder="请输入供应商名称"
            />
          </el-form-item>
          <el-form-item label="物料id" prop="materialId">
            <el-input v-model="form.materialId" placeholder="请输入物料id" />
          </el-form-item>
          <el-form-item label="物料编码" prop="materialCode">
            <el-input
              v-model="form.materialCode"
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item label="物料名称" prop="materialName">
            <el-input
              v-model="form.materialName"
              placeholder="请输入物料名称"
            />
          </el-form-item>

          <!-- <el-form-item label="父箱号id" prop="parentBoxId">
            <el-input v-model="form.parentBoxId" placeholder="请输入父箱号id" />
          </el-form-item> -->
          <el-form-item label="父箱号" prop="parentBoxNo">
            <el-input v-model="form.parentBoxNo" placeholder="请输入父箱号" />
          </el-form-item>
          <el-form-item label="数量" prop="qty">
            <el-input v-model="form.qty" placeholder="请输入数量" />
          </el-form-item>
          <el-form-item label="规格型号" prop="specification">
            <el-input
              v-model="form.specification"
              placeholder="请输入规格型号"
            />
          </el-form-item>
          <el-form-item label="单位" prop="materialUnit">
            <el-input v-model="form.materialUnit" placeholder="请输入单位" />
          </el-form-item>

          <el-form-item label="状态" prop="boxState">
            <el-select
              v-model="form.boxState"
              placeholder="请选择出入库状态"
              clearable
            >
              <el-option
                v-for="dict in dict.type.stock_in_state"
                :key="dict.id"
                :label="dict.label"
                :value="dict.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="生产日期" prop="dateCode">
            <el-date-picker
              clearable
              v-model="form.dateCode"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择生产日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="保质期" prop="expirationDate">
            <el-date-picker
              clearable
              v-model="form.expirationDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择保质期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="批次" prop="batchNo">
            <el-input v-model="form.batchNo" placeholder="请输入批次" />
          </el-form-item>
          <!-- <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item> -->
          <el-form-item label="组织" prop="comId">
            <el-input v-model="form.comId" placeholder="请输入组织" />
          </el-form-item>
          <el-form-item label="订单分类" prop="orderClass">
            <el-select
              v-model="form.orderClass"
              placeholder="请选择订单状态"
              clearable
            >
              <el-option
                v-for="dict in dict.type.order_class"
                :key="dict.id"
                :label="dict.label"
                :value="dict.label"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 数据导入 -->
      <el-dialog
        :title="upload.title"
        :visible.sync="upload.open"
        width="400px"
        append-to-body
        v-loading="loading"
      >
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :http-request="uploadFile"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <!-- <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
            </div> -->
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-debounce:3000="debounceClick"
            type="primary"
            @click="submitFileForm"
            >提 交</el-button
          >
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listwmsBoxPeriodOut,
  getwmsBoxPeriodOut,
  delwmsBoxPeriodOut,
  addwmsBoxPeriodOut,
  updatewmsBoxPeriodOut,
} from "@/api/system/WmsBoxPeriodOut";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { listHiprint } from "@/api/system/hiprint";
import { getToken } from "@/utils/auth";
import axios from "axios";

export default {
  name: "wmsBoxPeriodOut",
  dicts: ["box_state", "order_class"],
  data() {
    return {
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API + "/system/wmsBoxPeriodOut/importData",
      },
      showMoreConditions: false, // 控制是否显示更多条件
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 期出库存表格数据
      wmsBoxPeriodOutList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        batchNo: null,
        comId: null,
        orderClass: null,
        qrCode: null,
        palletNo: null,
        newBoxId: null,
        newBoxNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        purchaseNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getPrintList();
  },
  methods: {
    handleImport() {
      this.upload.title = "物料类型导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      const now = new Date();
      const year = now.getFullYear(); // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 获取月份，月份从0开始所以要+1
      const date = String(now.getDate()).padStart(2, "0"); // 获取日期
      const hours = String(now.getHours()).padStart(2, "0"); // 获取小时
      const minutes = String(now.getMinutes()).padStart(2, "0"); // 获取分钟
      const seconds = String(now.getSeconds()).padStart(2, "0"); // 获取秒钟
      const simpleFormat = `${year}${month}${date}${hours}${minutes}${seconds}`;
      this.download(
        "system/wmsBoxPeriodOut/importTemplate",
        {},
        `期出库存箱信息${simpleFormat}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    //参数必须是param，才能获取到内容
    uploadFile(param) {
      // 获取上传的文件名
      var file = param.file;
      //发送请求的参数格式为FormData
      const formData = new FormData();
      formData.append("file", file);
      // 调用param中的钩子函数处理各种情况，这样就可以用在组件中用钩子了。也可以用res.code==200来进行判断处理各种情况
      this.startUploadFile(formData, param)
        .then((res) => {
          param.onSuccess(res);
        })
        .catch((err) => {
          param.onError(err);
        });
    },

    // 上传的请求（这里是request封装的请求，封装了请求头等信息） responseType: 'blob' 这个是关键
    //request是封装的请求方法
    startUploadFile(query, param) {
      return axios({
        url: this.upload.url,
        responseType: "blob",
        method: "post",
        data: query,
        headers: {
          Authorization: "Bearer " + getToken(),
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    // 文件上传成功处理、错误
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      console.log(response.data.size, new Blob([response]));
      if (response.data.size != 0) {
        const blob = new Blob([response]);
        let url = window.URL.createObjectURL(
          new Blob([response.data], { type: ".xlsx" })
        );
        let a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.setAttribute("download", `物料类型导入失败.xlsx`);
        document.body.appendChild(a);
        a.click();
        url = window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        if (!response) {
          return;
        }
        const name = decodeURI(response);
      }
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      // this.starLoading = true;
      this.loadingInstance = this.starLoading(); //调用
      // this.$refs.upload.submit();
      // console.log(this.$refs.upload.submit());

      this.$refs.upload.submit();
      console.log(this.upload.open, "this.upload.open == false");

      if (this.upload.open == true) {
        this.upload.open = false;
        this.loadingInstance.closeLoading();
        this.getList();
      }
    },
    starLoading() {
      // 创建一个 loading 实例并赋值给 loading 变量
      let loading = this.$loading({
        text: "加载中", // 设置 loading 文本为 "加载中"
        spinner: "el-icon-loading", // 使用 Element UI 提供的加载图标
        background: "rgba(0, 0, 0, 0.7)", // 设置 loading 遮罩层背景色为半透明黑色
        target: document.querySelector("body"), // 将 loading 遮罩层挂载到页面 body 元素上
      });
      // 返回一个包含关闭 loading 遮罩层方法的对象
      return {
        // 方法用于关闭 loading 遮罩层
        closeLoading: () => {
          loading.close(); // 调用 loading 实例的 close 方法关闭遮罩层
        },
      };
    },

    debounceClick(v) {
      console.log("触发一下，间隔多少毫秒：", v);
    },

    getPrintList() {
      listHiprint({ code: "box" }).then((response) => {
        this.mypanel = JSON.parse(response.rows[0].printJson);
      });
    },
    toggleMoreConditions() {
      this.showMoreConditions = !this.showMoreConditions; // 切换显示状态
    },

    /** 查询期出库存列表 */
    getList() {
      this.loading = true;
      listwmsBoxPeriodOut(this.queryParams).then((response) => {
        this.wmsBoxPeriodOutList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        batchNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        orderClass: null,
        qrCode: null,
        palletNo: null,
        newBoxId: null,
        newBoxNo: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加期出库存";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getwmsBoxPeriodOut(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改期出库存";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatewmsBoxPeriodOut(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addwmsBoxPeriodOut(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除期出库存编号为"' + ids + '"的数据项？')
        .then(function () {
          return delwmsBoxPeriodOut(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/wmsBoxPeriodOut/export",
        {
          ...this.queryParams,
        },
        `期出库存_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
