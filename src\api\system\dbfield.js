import request from '@/utils/request'

// 查询数据库-库字段列表
export function listDbfield(query) {
  return request({
    url: '/system/dbfield/list',
    method: 'get',
    params: query
  })
}

// 查询数据库-库字段详细
export function getDbfield(id) {
  return request({
    url: '/system/dbfield/' + id,
    method: 'get'
  })
}

// 新增数据库-库字段
export function addDbfield(data) {
  return request({
    url: '/system/dbfield',
    method: 'post',
    data: data
  })
}

// 修改数据库-库字段
export function updateDbfield(data) {
  return request({
    url: '/system/dbfield',
    method: 'put',
    data: data
  })
}

// 删除数据库-库字段
export function delDbfield(id) {
  return request({
    url: '/system/dbfield/' + id,
    method: 'delete'
  })
}



// 查询数据库-库详细
export function getDbtable(id) {
  return request({
    url: '/system/dbtable/' + id,
    method: 'get'
  })
}

// 删除数据库-库
export function delDbtable(id) {
  return request({
    url: '/system/dbtable/' + id,
    method: 'delete'
  })
}