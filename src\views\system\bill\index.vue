<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="保修单号" prop="repairNo"  >
          <el-input
            v-model="queryParams.repairNo"
            placeholder="请输入保修单单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="设备编码" prop="equipCode">
          <el-input
            v-model="queryParams.equipCode"
            placeholder="请输入设备编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="报修人" prop="repairUser">
          <el-input
            v-model="queryParams.repairUser"
            placeholder="请输入报修人"
            clearable 
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="报修时间" prop="repairTime">
          <el-date-picker
            clearable
            v-model="queryParams.repairTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择报修时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="派工人员" prop="pgUser">
          <el-input
            v-model="queryParams.pgUser"
            placeholder="请输入派工人员"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="派工时间" prop="pgTime">
          <el-date-picker
            clearable
            v-model="queryParams.pgTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择派工时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="维修人员" prop="wxUser">
          <el-input
            v-model="queryParams.wxUser"
            placeholder="请输入维修人员"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="故障等级" prop="faultLevel">
          <el-input
            v-model="queryParams.faultLevel"
            placeholder="请输入故障等级"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="完成时间" prop="wxTime">
          <el-date-picker
            clearable
            v-model="queryParams.wxTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择维修完成时间"
          >
          </el-date-picker> 
        </el-form-item>
        <el-form-item label="维修结果" prop="wxResult">
          <el-input
            v-model="queryParams.wxResult"
            placeholder="请输入维修结果"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="验收人" prop="ysUser">
          <el-input
            v-model="queryParams.ysUser"
            placeholder="请输入验收人"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="验收时间" prop="ysTime">
          <el-input
            v-model="queryParams.ysTime"
            placeholder="请输入验收时间"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> 
        <!-- <el-form-item label="组织" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入组织"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:bill:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:bill:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:bill:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:bill:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="billList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="保修单单号" align="center" prop="repairNo" width="90"/>
        <el-table-column label="设备编码" align="center" prop="equipCode" />
        <el-table-column label="报修人" align="center" prop="repairUser" />
        <el-table-column
          label="报修时间"
          align="center"
          prop="repairTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.repairTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="报修描述" align="center" prop="repairDesc" />
        <el-table-column label="派工人员" align="center" prop="pgUser" />
        <el-table-column
          label="派工时间"
          align="center"
          prop="pgTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.pgTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维修人员" align="center" prop="wxUser" />
        <el-table-column label="故障等级" align="center" prop="faultLevel" />
        <el-table-column label="故障类型" align="center" prop="faultType" />
        <el-table-column
          label="维修完成时间"
          align="center"
          prop="wxTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.wxTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="维修结果" align="center" prop="wxResult" />
        <el-table-column label="故障原因" align="center" prop="faultReason" />
        <el-table-column label="维修说明" align="center" prop="wxDesc" />
        <el-table-column label="验收人" align="center" prop="ysUser" />
        <el-table-column label="验收时间" align="center" prop="ysTime" />
        <el-table-column label="验收描述" align="center" prop="ysDesc" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          min-width="180" 
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:bill:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:bill:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改设备报修单对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >

        <el-form ref="form" :model="form" :rules="rules">
        <el-row >
          <el-col :span="12">
          <el-form-item label="保修单单号" prop="repairNo">
            <el-input v-model="form.repairNo" placeholder="请输入保修单单号" style="width: 400px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="设备编码" prop="equipCode">
            <el-input v-model="form.equipCode" placeholder="请输入设备编码" style="width: 400px" />
          </el-form-item>
            </el-col>
        </el-row>

          <el-row >
            <el-col :span="12">
          <el-form-item label="报修人" prop="repairUser">
            <el-input v-model="form.repairUser" placeholder="请输入报修人" style="width: 400px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="报修时间" prop="repairTime">
            <el-date-picker
              clearable
              v-model="form.repairTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择报修时间"
              style="width: 400px"
            >
            </el-date-picker>
          </el-form-item>
           </el-col>
          </el-row>


          <el-form-item label="报修描述" prop="repairDesc">
            <el-input
              v-model="form.repairDesc"
              type="textarea"
              placeholder="请输入内容"
             
            />
          </el-form-item>

          <el-row >
            <el-col :span="12">
          <el-form-item label="派工人员" prop="pgUser">
            <el-input v-model="form.pgUser" placeholder="请输入派工人员"  style="width: 400px"/>
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="派工时间" prop="pgTime">
            <el-date-picker
              clearable
              v-model="form.pgTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择派工时间"
              style="width: 400px"
            >
            </el-date-picker>
          </el-form-item>
           </el-col>
          </el-row>

          <el-row >
            <el-col :span="12">
          <el-form-item label="维修人员" prop="wxUser">
            <el-input v-model="form.wxUser" placeholder="请输入维修人员"  style="width: 400px"/>
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="故障等级" prop="faultLevel">
            <el-input v-model="form.faultLevel" placeholder="请输入故障等级" style="width: 400px" />
          </el-form-item>
           </el-col>
          </el-row>

          <el-row >
            <el-col :span="12">
          <el-form-item label="维修完成时间" prop="wxTime">
            <el-date-picker
              clearable
              v-model="form.wxTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择维修完成时间"
              style="width: 400px"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="维修结果" prop="wxResult">
            <el-input v-model="form.wxResult" placeholder="请输入维修结果" style="width: 400px" />
          </el-form-item>
           </el-col>
          </el-row>


          <el-form-item label="故障原因" prop="faultReason">
            <el-input
              v-model="form.faultReason"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="维修说明" prop="wxDesc">
            <el-input
              v-model="form.wxDesc"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>

          <el-row >
            <el-col :span="12">
          <el-form-item label="验收人" prop="ysUser">
            <el-input v-model="form.ysUser" placeholder="请输入验收人" style="width: 400px"/>
          </el-form-item>
            </el-col>
            <el-col :span="12">
          <el-form-item label="验收时间" prop="ysTime">
            <el-input v-model="form.ysTime" placeholder="请输入验收时间" style="width: 400px" />
          </el-form-item> 
            </el-col>
          </el-row>


          <el-form-item label="验收描述" prop="ysDesc">
            <el-input
              v-model="form.ysDesc"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listBill,
  getBill,
  delBill,
  addBill,
  updateBill,
} from "@/api/system/bill";

export default {
  name: "Bill",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备报修单表格数据
      billList: [],
      //保修单单号数组
      codes: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        repairNo: null,
        equipCode: null,
        repairUser: null,
        repairTime: null,
        repairDesc: null,
        pgUser: null,
        pgTime: null,
        wxUser: null,
        faultLevel: null,
        faultType: null,
        wxTime: null,
        wxResult: null,
        faultReason: null,
        wxDesc: null,
        ysUser: null,
        ysTime: null,
        ysDesc: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        repairNo: [
          { required: true, trigger: "blur", message: "保养单单号不能为空" },
        ],
        equipCode: [
          { required: true, trigger: "blur", message: "设备编码不能为空" },
        ],
        repairUser: [
          { required: true, trigger: "blur", message: "报修人不能为空" },
        ],
        repairTime: [
          { required: true, trigger: "blur", message: "报修时间不能为空" },
        ],
        pgUser: [
          { required: true, trigger: "blur", message: "派工人员不能为空" },
        ],
        pgTime: [
          { required: true, trigger: "blur", message: "派工时间不能为空" },
        ],
        wxUser: [
          { required: true, trigger: "blur", message: "维修人员不能为空" },
        ],
        wxTime: [
          { required: true, trigger: "blur", message: "维修完成时间不能为空" },
        ],
        faultLevel: [
          { required: true, trigger: "blur", message: "故障等级不能为空" },
        ],
        ysUser: [
          { required: true, trigger: "blur", message: "验收人员不能为空" },
        ],
        ysTime: [
          { required: true, trigger: "blur", message: "验收时间不能为空" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备报修单列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then((response) => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        repairNo: null,
        equipCode: null,
        repairUser: null,
        repairTime: null,
        repairDesc: null,
        pgUser: null,
        pgTime: null,
        wxUser: null,
        faultLevel: null,
        faultType: null,
        wxTime: null,
        wxResult: null,
        faultReason: null,
        wxDesc: null,
        ysUser: null,
        ysTime: null,
        ysDesc: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.codes = selection.map((item) => item.repairNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备报修单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBill(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备报修单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBill(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.repairNo || this.codes;
      this.$modal
        .confirm('是否确认删除设备报修单编号为"' + codes + '"的数据项？')
        .then(function () {
          return delBill(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/bill/export",
        {
          ...this.queryParams,
        },
        `bill_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.ul-height {
  height: 62vh;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  margin-right: 3%;
  overflow-y:scroll;overflow-x:hidden;
  .colClass {
    height: 48%;
    margin-bottom:20px
  }
  ::v-deep .el-card {
    height: 450px;
  }
  .imgClass {
    width: 500px;
    // height: 100%;
    display: block;
    height: 200px;
     background: #f2f2f2;
     display: flex;
     justify-content: center;
     align-items: center;
     ::v-deep .el-image__inner{
      height: 100%;
      width: 60%;
     }
  }
  .contentTitle {
    font-weight: 500;
    font-size: 18px;
    margin-left: 0;
    margin-bottom: 3px;
  }
  .contentSpan {
    font-size: 15px;
    margin-left: 0;
  }
  li {
    border: 1px solid #dcdfe6;
    width: 15%;
    height: 410px;
    color: #1c1b1b;
    float: left;
    list-style: none;
    margin: 5px;
    cursor: pointer;
    overflow: hidden;
  }
}
.emptyClass{
	background-color: #fff;
	border-radius: 20px;
	width: 300px;
	height: 350px;
	margin: auto;
	// position: absolute;
	// top: 0;
	// left: 0;
	// right: 0;
	// bottom: 0;
}
</style>