<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="90px"
      >
        <el-form-item label="栈板号" prop="palletNo">
          <el-input
            v-model="queryParams.palletNo"
            placeholder="请输入栈板号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input
            v-model="queryParams.supplierCode"
            placeholder="请输入供应商编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="palletBind"
            >栈板标签绑定</el-button
          >
        </el-col>
      </el-row>

      <!-- 栈板标签绑定弹窗 -->
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        :size="'50%'"
        append-to-body
      >
        <el-form
          ref="boxForm"
          :model="boxForm"
          :rules="rules"
          @submit.native.prevent
        >
          <el-row>
            <el-table-column type="index" width="50" align="center" />
            <el-form-item label="箱号" prop="boxNo">
              <el-input
                v-model="InboxNo"
                placeholder="请输入箱号"
                @keydown.enter.native="handleEnter"
              />
            </el-form-item>
          </el-row>
          <el-table height="62vh" v-loading="loading" :data="boxList">
            <el-table-column label="箱号" align="center" prop="boxNo" />
            <el-table-column
              label="采购单号"
              align="center"
              prop="purchaseNo"
            />
            <el-table-column
              label="物料编码"
              align="center"
              prop="materialCode"
            />
            <el-table-column label="数量" align="center" prop="qty" />
            <el-table-column label="批次" align="center" prop="batchNo" />
          </el-table>
        </el-form>
        <div style="margin: 5px; display: flex; justify-content: end">
          <el-button type="primary" @click="submitFormBox">提 交</el-button>
          <el-button @click="cancelBox">取 消</el-button>
        </div>
      </el-dialog>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="palletList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="栈板号" align="center" prop="palletNo" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column
          label="供应商编码"
          align="center"
          prop="supplierCode"
          :width="tableWidth(palletList.map((item) => item.supplierCode))"
        />
        <el-table-column
          label="供应商名称"
          align="center"
          prop="supplierName"
          :width="tableWidth(palletList.map((item) => item.supplierName))"
        />
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          label="生产日期"
          align="center"
          prop="dateCode"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="保质期"
          align="center"
          prop="expirationDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.expirationDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="箱状态" align="center" prop="boxState">
          <template slot-scope="scope">
            <dict-tag
              type="success"
              :options="dict.type.box_state"
              :value="scope.row.boxState"
            />
          </template>
        </el-table-column>
        <el-table-column label="栈板状态" align="center" prop="palletState">
          <template slot-scope="scope">
            <dict-tag
              type="success"
              :options="dict.type.box_state"
              :value="scope.row.boxState"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />

        <el-table-column label="订单分类" align="center" prop="orderClass">
          <template slot-scope="scope">
            <dict-tag
              type="success"
              :options="dict.type.order_class"
              :value="scope.row.orderClass"
            />
          </template>
        </el-table-column>

        <el-table-column label="二维码" align="center" prop="qrCode" />
        <el-table-column label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handlePrint(scope.row)"
              >打印</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改栈板信息对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="栈板号" prop="palletNo" style="width: 240px">
            <el-input v-model="form.palletNo" placeholder="请输入栈板号" />
          </el-form-item>
          <el-form-item label="采购单号" prop="purchaseNo" style="width: 240px">
            <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
          </el-form-item>
          <el-form-item label="供应商id" prop="supplierId" style="width: 240px">
            <el-input v-model="form.supplierId" placeholder="请输入供应商id" />
          </el-form-item>
          <el-form-item
            label="供应商编码"
            prop="supplierCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.supplierCode"
              placeholder="请输入供应商编码"
            />
          </el-form-item>
          <el-form-item
            label="供应商名称"
            prop="supplierName"
            style="width: 240px"
          >
            <el-input
              v-model="form.supplierName"
              placeholder="请输入供应商名称"
            />
          </el-form-item>
          <el-form-item label="物料id" prop="materialId" style="width: 240px">
            <el-input v-model="form.materialId" placeholder="请输入物料id" />
          </el-form-item>
          <el-form-item
            label="物料编码"
            prop="materialCode"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialCode"
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item
            label="物料名称"
            prop="materialName"
            style="width: 240px"
          >
            <el-input
              v-model="form.materialName"
              placeholder="请输入物料名称"
            />
          </el-form-item>
          <el-form-item label="生产日期" prop="dateCode" style="width: 240px">
            <el-date-picker
              clearable
              v-model="form.dateCode"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择生产日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="保质期"
            prop="expirationDate"
            style="width: 240px"
          >
            <el-date-picker
              clearable
              v-model="form.expirationDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择保质期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="父箱号id"
            prop="parentBoxId"
            style="width: 240px"
          >
            <el-input v-model="form.parentBoxId" placeholder="请输入父箱号id" />
          </el-form-item>
          <el-form-item
            label="规格型号"
            prop="specification"
            style="width: 240px"
          >
            <el-input
              v-model="form.specification"
              placeholder="请输入规格型号"
            />
          </el-form-item>
          <el-form-item label="单位" prop="materialUnit" style="width: 240px">
            <el-input v-model="form.materialUnit" placeholder="请输入单位" />
          </el-form-item>
          <el-form-item label="箱状态" prop="boxState" style="width: 240px">
            <!-- <el-input v-model="form.boxState" placeholder="请输入箱状态" /> -->
            <el-select v-model="form.boxState">
              <el-option
                v-for="item in dict.type.box_state"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="栈板状态"
            prop="palletState"
            style="width: 240px"
          >
            <!-- <el-input v-model="form.palletState" placeholder="请输入栈板状态" /> -->

            <el-select v-model="form.palletState">
              <el-option
                v-for="item in dict.type.box_state"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="组织" prop="comId" style="width: 240px">
            <el-input v-model="form.comId" placeholder="请输入组织" />
          </el-form-item>
          <el-form-item label="订单分类" prop="orderClass" style="width: 240px">
            <!-- <el-input v-model="form.orderClass" placeholder="请输入订单分类-字典" /> -->
            <el-select v-model="form.orderClass">
              <el-option
                v-for="item in dict.type.order_class"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="二维码" prop="qrCode" style="width: 700px">
            <el-input
              v-model="form.qrCode"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listPallet,
  getPallet,
  delPallet,
  addPallet,
  updatePallet,
} from "@/api/system/pallet";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { listHiprint } from "@/api/system/hiprint";
import {
  listBox,
  listBoxBd,
  getBox,
  delBox,
  addBox,
  updateBox,
} from "@/api/system/box";

export default {
  name: "Pallet",
  dicts: ["box_state", "order_class"],
  data() {
    return {
      showMoreConditions: false, // 控制是否显示更多条件
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 栈板信息表格数据
      palletList: [],
      //栈板弹窗
      dialogVisible: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //箱信息表格数据
      boxList: [],
      //输入的箱号
      InboxNo: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        palletNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        palletState: null,
        comId: null,
        orderClass: null,
        qrCode: null,
        boxIds: [],
        qty: null,
      },
      //箱信息查询参数
      queryParamsBox: {
        pageNum: 1,
        pageSize: 10,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        batchNo: null,
        comId: null,
        orderClass: null,
      },
      // 表单参数
      form: {},
      //箱信息表单参数
      boxForm: {},
      mypanel: {},
      // 表单校验
      rules: {
        palletNo: [
          { required: true, message: "栈板号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getPrintList();
  },
  methods: {
    getPrintList() {
      listHiprint({ code: "pallet" }).then((response) => {
        this.mypanel = JSON.parse(response.rows[0].printJson);
      });
    },
    toggleMoreConditions() {
      this.showMoreConditions = !this.showMoreConditions; // 切换显示状态
    },
    //回车判断事件
    handleEnter() {
      this.queryParamsBox.boxNo = this.InboxNo;
      this.InboxNo = null;
      this.getListBox();
    },
    handleQueryBox() {
      this.queryParamsBox.pageNum = 1;
      this.getListBox();
    },
    /** 查询箱信息列表 */
    getListBox() {
      this.loading = true;
      listBoxBd(this.queryParamsBox).then((response) => {
        response.rows.forEach((newItem) => {
          if (newItem == null) {
            this.$message.warning(`该箱号不存在，请重新输入！`);
            return;
          }

          //   if(this.boxList.length == 0){
          //     const has2 = this.palletList.some(
          //     (Item) => Item.palletNo === newItem.palletNo
          //   );
          //  if (has2) {
          //     this.$message.warning(`该箱信息已绑定其他栈板！`);
          //     return;
          //   }else{
          //     return;
          //   }
          //   }

          // 如果has为true，说明此数据已绑定其他栈板
          const has = this.palletList.some(
            (Item) => Item.palletNo === newItem.palletNo
          );
          //如果exists为false，说明没有重复的id，直接push
          const exists = this.boxList.some(
            (existingItem) => existingItem.id === newItem.id
          );
          if (!has && !exists) {
            this.boxList.push(newItem);
            this.queryParams.boxIds.push(newItem.id);
            console.info(
              "this.queryParams.boxIds的值为：",
              this.queryParams.boxIds
            );
          } else if (has && !exists) {
            this.$message.warning(`该箱信息已绑定其他栈板！`);
            return;
          } else if (!has && exists) {
            this.$message.warning(`该箱信息已存在！`);
            return;
          }
        }),
          (this.total = response.total);
        this.loading = false;
      });
    },
    cancelBox() {
      this.dialogVisible = false;
      //this.reset();
    },
    /** 查询栈板信息列表 */
    getList() {
      this.loading = true;
      listPallet(this.queryParams).then((response) => {
        console.log("后端返回的栈板数据：", response.rows);
        // this.palletList = this.sortArrayByField(response.rows, "createTime");
        this.palletList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        palletNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        palletState: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        orderClass: null,
        qrCode: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.codes = selection.map((item) => item.palletNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //打印按钮操作
    handlePrint(row) {
      hiprint.init();
      //调用接口获取数据
      var hiprintTemplate = new hiprint.PrintTemplate({
        template: this.mypanel,
        settingContainer: "#templateDesignDiv",
      });
      hiprintTemplate.print([row]);
    },
    //栈板标签绑定
    palletBind() {
      this.dialogVisible = true;
      this.title = "栈板标签绑定";
      this.InboxNo = null;
      this.boxList = [];
      this.queryParamsBox.boxNo = null;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加栈板信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPallet(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改栈板信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePallet(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPallet(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮 */
    submitFormBox() {
      //把选中的箱数据赋值给栈板数据
      this.form.purchaseNo = this.boxList[0].purchaseNo;
      this.form.supplierId = this.boxList[0].supplierId;
      this.form.supplierCode = this.boxList[0].supplierCode;
      this.form.supplierName = this.boxList[0].supplierName;
      this.form.materialId = this.boxList[0].materialId;
      this.form.materialCode = this.boxList[0].materialCode;
      this.form.materialName = this.boxList[0].materialName;
      this.form.dateCode = this.boxList[0].dateCode;
      this.form.expirationDate = this.boxList[0].expirationDate;
      this.form.parentBoxId = this.boxList[0].parentBoxId;
      this.form.specification = this.boxList[0].specification;
      this.form.materialUnit = this.boxList[0].materialUnit;
      this.form.boxState = this.boxList[0].boxState;
      this.form.palletState = this.boxList[0].boxState;
      this.form.remark = this.boxList[0].remark;
      this.form.comId = this.boxList[0].comId;
      this.form.orderClass = this.boxList[0].orderClass;
      this.form.qrCode = this.boxList[0].qrCode;
      //this.form.boxId.push(this.boxList[0].id);
      this.form.qty = this.boxList.reduce(
        (total, item) => total + (item.qty || 0),
        0
      );
      this.form.boxIds = this.queryParams.boxIds;
      console.log("this.form.qty的值为：", this.form.qty);

      this.$refs["boxForm"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePallet(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.getList();
            });
          } else {
            addPallet(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.getList();
            });
            // //绑定栈板号
            // this.boxForm.palletNo = this.form.palletNo;
            // updateBox(this.boxForm).then((response) => {
            //   this.open = false;
            //   //this.getList();
            // });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.palletNo || this.codes;
      this.$modal
        .confirm('是否确认删除栈板号为"' + codes + '"的数据项？')
        .then(function () {
          return delPallet(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/pallet/export",
        {
          ...this.queryParams,
        },
        `栈板信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
