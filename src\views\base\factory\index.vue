<template>
  <div class="app-container">
    <div class="app-container-div">
      <div style="padding: 0px; background: #fff; border-radius: 5px">
        <el-row :gutter="20">
          <el-col :span="5" :xs="24" v-loading="treeLoading">
            <div class="head-container">
              <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            </div>
            <el-tree :data="listTree" node-key="id" :props="defaultProps" @node-click="handleNodeClick"
              :filter-node-method="filterNode" default-expand-all :expand-on-click-node="false"
              :highlight-current="true" ref="tree"></el-tree>
          </el-col>
          <el-col :span="19" :xs="24" v-if="level == null || level == 'LEVEL_1'">
            <div v-if="level == null || level == 'LEVEL_1'" style="padding: 10px; 
               font-size: 14px;
               font-weight: normal;
               color: #606266;
               display: flex;
               justify-content: space-around;
               ">
              <span>企业编码：{{ parentData.code }}</span>
              <span>企业名称：{{ parentData.name }}</span>
            </div>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                  v-hasPermi="['system:factory:add']">新增</el-button>
              </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <el-table fit :data="factoryList" @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'createTime', order: 'descending' }">
              <el-table-column type="index" width="55" align="center" />
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="工厂编码" :width="tableWidth(factoryList.map(x => x.factoryCode))" align="center"
                prop="factoryCode" />
              <el-table-column label="工厂名称" align="center" prop="factoryName"
                :width="tableWidth(factoryList.map(x => x.factoryName))" />
              <!-- <el-table-column label="祖级列表" align="center" prop="ancestors" /> -->
              <el-table-column label="部门名称" align="center" prop="deptName" />
              <el-table-column label="显示顺序" align="center" prop="orderNum" />
              <el-table-column label="负责人" align="center" prop="leader" />
              <el-table-column label="联系电话" align="center" prop="phone"
                :width="tableWidth(factoryList.map(x => x.phone))" />
              <el-table-column label="备注" :width="tableWidth(factoryList.map(x => x.remark))" align="center"
                prop="remark" />
              <el-table-column label="状态" align="center" prop="status" />
              <el-table-column label="操作" fixed="right" :width="tableWidth()" align="center"
                class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:factory:edit']">修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                    v-hasPermi="['system:factory:remove']">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize" @pagination="getList" />
            <!-- 添加或修改工厂对话框 -->
            <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
              <el-form ref="form" :model="form" :rules="rules">
                <el-collapse v-model="activeNames">
                  <el-collapse-item title="工厂信息" name="1">
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="工厂编码" prop="factoryCode" style="width: 240px">
                          <el-input v-model="form.factoryCode" placeholder="请输入工厂编码" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="工厂名称" prop="factoryName" style="width: 240px">
                          <el-input v-model="form.factoryName" placeholder="请输入工厂名称" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="部门名称" prop="deptName" style="width: 240px">
                          <el-input v-model="form.deptName" placeholder="请输入部门名称" />
                        </el-form-item>
                      </el-col>
                      
                      <el-col :span="12">
                        <el-form-item label="显示顺序" prop="orderNum" style="width: 240px">
                          <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="负责人" prop="leader" style="width: 240px">
                          <el-input v-model="form.leader" placeholder="请输入负责人" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="联系电话" prop="phone" style="width: 240px">
                          <el-input v-model="form.phone" placeholder="请输入联系电话" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <!-- 部门双向绑定的字段并非parentData.name 这里只做展示用 真实表达你的parentId已完成赋值 -->
                        <el-form-item label="父部门" prop="parentId" style="width: 240px;">
                          <el-input disabled v-model="parentData.name" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="备注" prop="remark" style="width: 700px">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                    </el-form-item>
                  </el-collapse-item>
                </el-collapse>
                <div class="demo-drawer__footer">
                  <el-button type="primary" @click="submitForm">确 定</el-button>
                  <el-button @click="cancel">取 消</el-button>
                </div>
              </el-form>
            </el-drawer>
          </el-col>
          <el-col :span="19" :xs="24" v-if="level == 'LEVEL_2'">
            <Shop @flushs="getTree" :parentData="parentData" :key="parentData.id"></Shop>
          </el-col>
          <el-col :span="19" :xs="24" v-if="level == 'LEVEL_3'">
            <Lineses @flushs="getTree" :parentData="parentData" :key="parentData.id"></Lineses>
          </el-col>
          <el-col :span="19" :xs="24" v-if="level == 'LEVEL_4'">
            <Station @flushs="getTree" :parentData="parentData" :key="parentData.id"></Station>
          </el-col>
          <el-col :span="19" :xs="24" v-if="level == 'LEVEL_5' || level == 'LEVEL_6'">
            <Device @flushs="getTree" :parentData="parentData" :key="parentData.id"></Device>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import {
  listFactory,
  getFactory,
  delFactory,
  addFactory,
  updateFactory,
  getTreeList,
} from "@/api/system/factory";
import Shop from "../componets/shop.vue";//车间
import Lineses from "../componets/lineses.vue";//产线
import Station from "../componets/station.vue";//工位
import Device from "../componets/device.vue";//设备
export default {

  components: {
    Shop,
    Lineses,
    Station,
    Device
  },
  name: "Factory",
  data() {
    return {
      activeNames: ["1"],
      parentData: {
        code: '请先在左侧进行选择',
        name: '请先在左侧进行选择',
        id: '0',
      },
      level: null,
      listTree: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      filterText: '',
      // ----------
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工厂表格数据
      factoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        factoryCode: null,
        factoryName: null,
        parentId: null,
        ancestors: null,
        deptName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
      },
      treeLoading: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        factoryCode: [
          { required: true, trigger: "blur", message: "工厂编码不能为空" },
        ],
        factoryName: [
          { required: true, trigger: "blur", message: "工厂名称不能为空" },
        ],
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    // this.getList();
    this.getTree();
  },
  methods: {



    //获取树结构列表
    getTree() {
      this.treeLoading = true;
      getTreeList().then((response) => {
        this.listTree = response;
        this.treeLoading = false;
      });
    },
    handleNodeClick(e) {
      this.level = e.level;//控制v-if的 勿删
      this.parentData.id = e.id;
      this.parentData.code = e.code;
      this.parentData.name = e.label;

      //默认界面 因为工厂不是组件所以需要自己调用
      if (e.level == "LEVEL_1") {
        this.queryParams.parentId = e.id;
        this.getList();
        this.queryParams.parentId = null;
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /** 查询工厂列表 */
    getList() {
      // this.getTree();

      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      this.queryParams.parentId = this.parentData.id;
      listFactory(this.queryParams).then((response) => {
        this.factoryList = response.rows;
        this.total = response.total;

      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        factoryCode: null,
        factoryName: null,
        parentId: null,
        ancestors: null,
        deptName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (!this.parentData.id || this.level != "LEVEL_1") {
        return this.$modal.msg("请选择企业节点");
      }
      this.reset();
      this.form.parentId = this.parentData.id;
      this.open = true;
      this.title = "添加工厂";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFactory(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工厂";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateFactory(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getTree();
            });
          } else {
            addFactory(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getTree();
            });
          }
        }

      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除工厂编号为"' + row.factoryCode + '"的数据项？')
        .then(function () {
          return delFactory(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/factory/export",
        {
          ...this.queryParams,
        },
        `factory_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
