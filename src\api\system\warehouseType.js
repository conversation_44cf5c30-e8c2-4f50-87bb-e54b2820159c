import request from '@/utils/request'

// 查询仓库类型列表
export function listWarehouseType(query) {
  return request({
    url: '/system/warehouseType/list',
    method: 'get',
    params: query
  })
}

// 查询仓库类型详细
export function getWarehouseType(id) {
  return request({
    url: '/system/warehouseType/' + id,
    method: 'get'
  })
}

// 新增仓库类型
export function addWarehouseType(data) {
  return request({
    url: '/system/warehouseType',
    method: 'post',
    data: data
  })
}

// 修改仓库类型
export function updateWarehouseType(data) {
  return request({
    url: '/system/warehouseType',
    method: 'put',
    data: data
  })
}

// 删除仓库类型
export function delWarehouseType(id) {
  return request({
    url: '/system/warehouseType/' + id,
    method: 'delete'
  })
}
