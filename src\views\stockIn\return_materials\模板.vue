<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input v-model="queryParams.stockInNo" placeholder="请输入入库单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="入库日期" prop="stockInDate">
          <el-date-picker clearable v-model="queryParams.stockInDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择入库日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="入库状态" prop="stockInState">
          <el-select v-model="queryParams.stockInState" placeholder="请选择入库状态" style="width: 160px;">
            <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="质检状态" prop="qualityState">
          <el-select v-model="queryParams.qualityState" placeholder="请选择质检状态" style="width: 160px;">
            <el-option v-for="dict in dict.type.quality_state" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:wms_outsourcing_return:add']">新增</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:wms_outsourcing_return:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:wms_outsourcing_return:remove']">删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:wms_outsourcing_return:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="wms_outsourcing_returnList"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="入库单号" align="center" prop="stockInNo" />
        <el-table-column label="入库状态" align="center" prop="stockInState">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.stock_in_state" :value="scope.row.stockInState" />
          </template>
        </el-table-column>
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <el-table-column label="创建者" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新者ID" align="center" prop="updateBy" />
        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column width="230" label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-edit"
              @click="updateType(scope.row, 'STOCK_PENDING')" v-hasPermi="['system:wms_outsourcing_return:edit']">录入完成</el-button>
            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-edit"
              @click="handleUpdate(scope.row)" v-hasPermi="['system:wms_outsourcing_return:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate2(scope.row)"
              v-hasPermi="['system:wms_outsourcing_return:edit']">详情</el-button>

            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-edit"
              @click="updateType(scope.row, 'HAVE_BEEN')" v-hasPermi="['system:stock_in:remove']">作废</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
         <!-- 添加或修改委外退货对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'60%'" append-to-body>
        <el-form :label-position="labelPosition" :inline="true" ref="form" :model="form" :rules="rules">
          <el-form-item label="仓库" prop="warehouseId">
            <el-select @change="ckChange" v-model="form.warehouseName" placeholder="请选择仓库">
              <el-option v-for="(item, index) in warehouseList" :key="index" :label="item.warehouseName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库类型" prop="stockInType"   >
            <el-select disabled v-model="form.stockInType" placeholder="请选择入库类型"   >
              <el-option v-for="dict in dict.type.stock_in_type" :key="dict.value" :label="dict.label"
                :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="入库日期" prop="stockInDate"   >
            <el-date-picker clearable v-model="form.stockInDate" type="date" value-format="yyyy-MM-dd"
              placeholder="请选择入库日期">
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="入库状态" prop="stockInState"   >
            <el-select v-model="form.stockInState" placeholder="请选择入库状态"   >
              <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
                :value="dict.value">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-divider content-position="center">入库单明细信息</el-divider>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini"
                @click="handleAddWmsStockInDetail">添加</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" icon="el-icon-delete" size="mini"
                @click="handleDeleteWmsStockInDetail">删除</el-button>
            </el-col>
          </el-row>
          <el-table :data="wmsStockInDetailList" :row-class-name="rowWmsStockInDetailIndex"
            @selection-change="handleWmsStockInDetailSelectionChange" ref="wmsStockInDetail">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="退料行号" prop="stockInLine">
              <template slot-scope="scope">
                <el-input v-model="scope.row.stockInLine" placeholder="请输入行号" />
              </template>
            </el-table-column>
            <el-table-column label="物料编码" prop="materialCode">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialCode" placeholder="请输入物料编码" />
              </template>
            </el-table-column>
            <el-table-column label="物料名称" prop="materialName">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" />
              </template>
            </el-table-column>
            <el-table-column label="规格型号" prop="specification">
              <template slot-scope="scope">
                <el-input v-model="scope.row.specification" placeholder="请输入规格型号" />
              </template>
            </el-table-column>
            <el-table-column label="单位" prop="materialUnit">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialUnit" placeholder="请输入单位" />
              </template>
            </el-table-column>
            <el-table-column label="数量" prop="qty">
              <template slot-scope="scope">
                <el-input v-model="scope.row.qty" placeholder="请输入数量" />
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <el-drawer :title="title2" :visible.sync="open2" :size="'75%'" append-to-body>
        <el-form ref="form" :model="form" size="small" :label-position="labelPosition" :inline="true"
          label-width="100px">
          <el-form-item label="入库单号" prop="stockInNo"   >
            <el-input disabled v-model="form.stockInNo" placeholder="" />
          </el-form-item>
          <el-form-item label="采购单号" prop="purchaseNo"   >
            <el-input disabled v-model="form.purchaseNo" placeholder="" />
          </el-form-item>
          <el-form-item label="收货单id" prop="reviceId"   >
            <el-input disabled v-model="form.reviceId" placeholder="" />
          </el-form-item>
          <el-form-item label="供应商编码" prop="supplierCode"   >
            <el-input disabled v-model="form.supplierCode" placeholder="" />
          </el-form-item>
          <el-form-item label="供应商名称" prop="supplierName"   >
            <el-input disabled v-model="form.supplierName" placeholder="" />
          </el-form-item>
          <el-form-item label="入库日期" prop="stockInDate"   >
            <el-date-picker disabled clearable v-model="form.stockInDate" type="date" value-format="yyyy-MM-dd"
              placeholder="">


            </el-date-picker>
          </el-form-item>
          <el-form-item label="入库状态" prop="stockInState"   >
            <el-select disabled v-model="form.stockInState" placeholder=""   >
              <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
                :value="dict.value"></el-option>
            </el-select>
          </el-form-item>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="入库明细" name="first">
              <StockDetail :stock_in_id="stock_in_id" :key="refreshValue" :activeName="activeName" />
            </el-tab-pane>
            <el-tab-pane label="标签明细" name="second">
              <div style="display: flex;justify-content: space-evenly;">
                <div style="width: 48%;">
                  <StockDetail @sendDetailId="receiveDetailData" :stock_in_id="stock_in_id" :key="refreshValue"
                    :activeName="activeName" />
                </div>
                <div style="width: 48%;">
                  <StockBox :stock_detail_id="stock_detail_id" :key="stock_detail_id" />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </el-drawer>

    </div>
  </div>
</template>
<script>

import { listWms_outsourcing_return, getWms_outsourcing_return, delWms_outsourcing_return, addWms_outsourcing_return, updateWms_outsourcing_return } from "@/api/system/wms_outsourcing_return";

import StockDetail from "../stock_detail/componet.vue";
import StockBox from "../stock_box/stockBoxComponet.vue";
import {
  listWarehouse
} from "@/api/system/warehouse";
export default {
  name: "wms_outsourcing_return",
  dicts: ['stock_in_type', 'stock_in_state', 'quality_state'],
  components: {
    StockDetail,
    StockBox, // 注册子组件  
  },
  data() {
    return {
      open2: false,
      title2: "",
      // 表单查询时候的展示更多条件的boolean
      moreSelect: false,
      //传给箱表的id 来源于detail组件
      stock_detail_id: '',
      flushState: true,
        // 主表具体的id
      stock_in_id: "",
      //tabs标签的name
      activeName: 'first',
      labelPosition: 'top',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockInDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 委外退货表格数据
      wms_outsourcing_returnList: [],
      // 入库单明细表格数据
      wmsStockInDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "SALE",
        stockInDate: null,
        stockInState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        comId: null,
      },
      //仓库列表
      warehouseList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {

      }
    };
  },
  created() {
    this.getList();

    this.ckList();
  },
  methods: {
   //仓库数组选中改变的时候
ckChange(val) {
      const item = this.warehouseList.find(item => item.id === val);
      if (item) {
        this.form.warehouseCode = item.warehouseCode;
        this.form.warehouseId = item.id;
        this.form.warehouseName = item.warehouseName;
      }
    },
    /** 查询仓库列表 */
    ckList() {
      let qur = {
        pageNum: 1,
        pageSize: 1000,
        warehouseCode: null,
        warehouseName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
      };
      listWarehouse(qur).then((response) => {
        this.warehouseList = response.rows;
      });
    },
     // 子传父 方法处理
    receiveDetailData(data) {
      this.stock_detail_id = data;
    },
    handleClick() {
      if (this.activeName == "first") {
        this.stock_detail_id = null;
      }
    },
    /** 修改按钮操作 */
    handleUpdate2(row, type) {
      this.reset();
      this.stock_in_id = row.id;
      const id = row.id || this.ids
      getWms_outsourcing_return(id).then(response => {
        this.form = response.data;
        this.wmsStockInDetailList = response.data.wmsStockInDetailList;
        this.open2 = true;
        this.title2 = "委外退货详情";
      });
    },
    //作废或者完成录入的时候的更新方法
    updateType(row, type) {
      var content = "";
      content = type == "HAVE_BEEN" ? "作废" : "录入完成"
      this.$modal.confirm('是否确' + content + '改编号为"' + row.id + '"的数据项？').then(function () { }).then(() => {
        getWms_outsourcing_return(row.id).then(response => {
          this.form = response.data;
          this.form.stockInState = type;
          updateWms_outsourcing_return(this.form).then(response => {
            this.getList();
            this.$modal.msgSuccess(content + "成功");
          });
        });
      }).catch(() => { });
    },
     //判别销毁或者作废按钮应不应该出来的方法
    distroyCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "PUT_STORAGE":
          return false;
        case "BE_IN_STORAGE":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        default:
          return true;
      }
    },
     // 判断关闭按钮是否应该出来
    closeCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "CLOSED":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        case "RETURNED":
          return false;
        default:
          return true;
      }
    },
    /** 查询委外退货列表 */
    getList() {
      this.loading = true;
      listWms_outsourcing_return(this.queryParams).then(response => {
        this.wms_outsourcing_returnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "SALE",
        stockInDate: null,
        stockInState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.wmsStockInDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加委外退货";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWms_outsourcing_return(id).then(response => {
        this.form = response.data;
        this.wmsStockInDetailList = response.data.wmsStockInDetailList;
        this.open = true;
        this.title = "修改委外退货";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.wmsStockInDetailList = this.wmsStockInDetailList;
          if (this.form.id != null) {
            updateWms_outsourcing_return(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWms_outsourcing_return(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除委外退货编号为"' + ids + '"的数据项？').then(function () {
        return delWms_outsourcing_return(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 入库单明细序号 */
    rowWmsStockInDetailIndex({
      row,
      rowIndex
    }) {
      row.index = rowIndex + 1;
    },
    /** 入库单明细添加按钮操作 */
    handleAddWmsStockInDetail() {
      let obj = {};
      obj.purchaseDetailId = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.incomingQty = "";
      obj.stockInLine = "";
      obj.stockInState = "";
      obj.qualityState = "";
      obj.qualifiedQty = "";
      obj.batchNo = "";
      obj.remark = "";
      obj.comId = "";
      obj.stockInNo = "";
      this.wmsStockInDetailList.push(obj);
    },
    /** 入库单明细删除按钮操作 */
    handleDeleteWmsStockInDetail() {
      if (this.checkedWmsStockInDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单明细数据");
      } else {
        const wmsStockInDetailList = this.wmsStockInDetailList;
        const checkedWmsStockInDetail = this.checkedWmsStockInDetail;
        this.wmsStockInDetailList = wmsStockInDetailList.filter(function (item) {
          return checkedWmsStockInDetail.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsStockInDetailSelectionChange(selection) {
      this.checkedWmsStockInDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/wms_outsourcing_return/export', {
        ...this.queryParams
      }, `委外退货_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>