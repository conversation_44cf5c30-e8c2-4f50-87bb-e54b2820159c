import request from '@/utils/request'

// 查询检验方法列表
export function listMenthod(query) {
  return request({
    url: '/quality/menthod/list',
    method: 'get',
    params: query
  })
}

// 查询检验方法详细
export function getMenthod(id) {
  return request({
    url: '/quality/menthod/' + id,
    method: 'get'
  })
}

// 新增检验方法
export function addMenthod(data) {
  return request({
    url: '/quality/menthod',
    method: 'post',
    data: data
  })
}

// 修改检验方法
export function updateMenthod(data) {
  return request({
    url: '/quality/menthod',
    method: 'put',
    data: data
  })
}

// 删除检验方法
export function delMenthod(id) {
  return request({
    url: '/quality/menthod/' + id,
    method: 'delete'
  })
}
