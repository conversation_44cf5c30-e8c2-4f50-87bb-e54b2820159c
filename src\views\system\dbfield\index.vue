<template>
  <div class="app-container">
    <div class="app-container-div">
      <!-- 添加 div 背景样式 -->
      <div style="padding: 0px; background: #fff; border-radius: 5px">
        <el-row :gutter="20">
          <el-col :span="6" :xs="24">
            <div class="head-container">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText"
              ></el-input>
            </div>
            <!-- <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></el-tree> -->

            <el-table
              height="80vh"
              :data="dataTree"
              style="width: 100%; margin-bottom: 20px"
              row-key="id"
              default-expand-all
              :tree-props="{ children: 'children' }"
              :show-header="false"
              @row-click="handleNodeClick"
            >
              <!-- <el-table-column prop="id" label="序号"  width="50">
              </el-table-column> -->
              <el-table-column label="表名" width=300%>
                <template #default="{ row }">
                  {{ row.comment }}({{ row.label }})
                </template>
              </el-table-column>

              <el-table-column label="操作" width=90%>
                <template slot-scope="scope">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleUpdate1(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    size="small"
                    type="text"
                    @click="handleDelete1(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <!-- <el-tree 
              class="filter-tree"
              :data="dataTree"
              :props="defaultProps"
              default-expand-all
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              ref="tree"
            >
          

            </el-tree> -->
          </el-col>
          <el-col :span="18" :xs="24">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
              style="margin-bottom: 0px"
            >
              <!-- {{ queryParams.tableName }}: -->
              {{ queryParams.tableEn }}
            </el-form>
            <!-- 将表格附属按纽添加左空间和顶空间 -->
            <el-row :gutter="10" class="mb8" style="padding-left: 10px">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  v-hasPermi="['system:dbfield:add']"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['system:dbfield:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['system:dbfield:remove']"
                  >删除</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
              ></right-toolbar>
            </el-row>

            <el-table
              height="62vh"
              v-loading="loading"
              :data="dbfieldList"
              @selection-change="handleSelectionChange"
              style="padding-left: 5px"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column
                label="序号"
                type="index"
                min-width="5%"
                class-name="allowDrag"
              />
              <!-- <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="表英文" align="center" prop="tableEn" />
      <el-table-column label="表名称" align="center" prop="tableName" /> -->
              <el-table-column label="字段名">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldEn"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="字段注释">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldName"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="字段类型">
                <template slot-scope="scope">
                  <!-- <el-input v-model="scope.row.fieldType"></el-input> -->
                  <el-select
                    v-model="scope.row.fieldType"
                    placeholder="请选择字段类型"
                  >
                    <el-option
                      v-for="dict in dict.type.field_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <!-- <el-table-column label="字段类型" align="center" prop="fieldType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.field_type" :value="scope.row.fieldType"/>
        </template>
      </el-table-column> -->
              <el-table-column label="字段长度">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.fieldLength"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="小数点">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.point"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="是否NULL" align="center" prop="isNull">
                <template slot-scope="scope">
                  <el-checkbox
                    true-label="1"
                    false-label="0"
                    v-model="scope.row.isNull"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="是否主键" align="center" prop="isKey">
                <template slot-scope="scope">
                  <el-checkbox
                    true-label="1"
                    false-label="0"
                    v-model="scope.row.isKey"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="默认值">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.defaultValue"></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="默认值" align="center" prop="defaultValue" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:dbfield:edit']"
                    >修改</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['system:dbfield:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </el-row>
        <!-- 添加或修改数据库-库字段对话框 -->
        <el-drawer
          :title="title"
          :visible.sync="open"
          :direction="direction"
          size="50%"
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="表英文" prop="tableEn">
                  <el-input v-model="form.tableEn" placeholder="请输入表英文" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="表名称" prop="tableName">
                  <el-input
                    v-model="form.tableName"
                    placeholder="请输入表名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="字段名" prop="fieldEn">
                  <el-input v-model="form.fieldEn" placeholder="请输入字段名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字段名称" prop="fieldName">
                  <el-input
                    v-model="form.fieldName"
                    placeholder="请输入字段名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="字段类型" prop="fieldType">
              <el-select v-model="form.fieldType" placeholder="请选择字段类型">
                <el-option
                  v-for="dict in dict.type.field_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="字段长度" prop="fieldLength">
                  <el-input
                    v-model="form.fieldLength"
                    placeholder="请输入字段长度"
                    oninput="value=value.replace(/[^\d]/g,'')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="小数点" prop="point">
                  <el-input
                    v-model="form.point"
                    placeholder="请输入小数点"
                    oninput="value=value.replace(/[^\d]/g,'')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="是否NULL"
                  prop="isNull"
                  label-width="200px"
                >
                  <el-checkbox
                    v-model="form.isNull"
                    true-label="1"
                    false-label="0"
                  ></el-checkbox>
                  <!-- <el-input
                v-model="form.isNull"
                type="textarea"
                placeholder="请输入内容"
              /> -->
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="键" prop="isKey">
                  <el-checkbox
                    v-model="form.isKEY"
                    true-label="1"
                    false-label="0"
                  ></el-checkbox>
                  <!-- <el-input
                v-model="form.isKey"
                type="textarea"
                placeholder="请输入内容"
              /> -->
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="默认值" prop="defaultValue">
              <el-input
                v-model="form.defaultValue"
                placeholder="请输入默认值"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <!-- <el-form-item label="删除标志" prop="delFlag">
              <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
            </el-form-item> -->
          </el-form>
          <div style="right: 10px">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-drawer>

        <!-- 添加或修改数据库-库对话框 -->
        <el-dialog
          :title="title"
          :visible.sync="opentable"
          width="500px"
          append-to-body
        >
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="库名称" prop="dataName">
              <!-- <el-input v-model="form.dataName" placeholder="请输入库名称" /> -->
              <el-select
                v-model="form.dataName"
                placeholder="请选择"
                style="min-width: none; max-width: 240px"
              >
                <el-option
                  v-for="item in dbDatabaseList"
                  :key="item.id"
                  :label="item.databaseEn"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="表英文" prop="tableEn">
              <el-input
                v-model="form.tableEn"
                placeholder="请输入表英文"
                style="min-width: none; max-width: 240px"
              />
            </el-form-item>
            <el-form-item label="表名称" prop="tableName">
              <el-input
                v-model="form.tableName"
                placeholder="请输入表名称"
                style="min-width: none; max-width: 240px"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
                style="min-width: none; max-width: 240px"
              />
            </el-form-item>
            <!-- <el-form-item label="删除标志" prop="delFlag">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item> -->
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTable">确 定</el-button>
            <el-button @click="canceltable">取 消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {
  listDbfield,
  getDbfield,
  delDbfield,
  addDbfield,
  updateDbfield,
} from "@/api/system/dbfield";
import { listDbdatabaseTree } from "@/api/system/dbdatabase";

import {
  getDbtable,
  delDbtable,
  addDbtable,
  updateDbtable,
} from "@/api/system/dbtable";
import { listDbdatabase } from "@/api/system/dbdatabase";

export default {
  name: "Dbfield",
  dicts: ["field_type"],
  data() {
    return {
      filterText: "",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据库-库字段表格数据
      dbfieldList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      opentable: false,
      direction: "rtl",
      dataTree: [
        {
          id: 1,
          label: "TPM平台",
          children: [
            {
              id: "tpm_equip",
              label: "设备信息表",
            },
            {
              id: 2,
              label: "444",
            },
          ],
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tableEn: null,
        tableName: null,
        fieldEn: null,
        fieldName: null,
        fieldType: null,
        fieldLength: null,
        point: null,
        isNull: null,
        isKey: null,
        defaultValue: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tableEn: [{ required: true, message: "表名不能为空", trigger: "blur" }],
        tableName: [
          { required: true, message: "表注释不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.init();
  },
  methods: {
    init() {
      listDbdatabaseTree(this.queryParams).then((response) => {
        this.dataTree = response.data;
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /** 查询数据库-库字段列表 */
    getList() {
      this.loading = true;
      listDbfield(this.queryParams).then((response) => {
        this.dbfieldList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表取消按钮
    canceltable() {
      this.opentable = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tableEn: null,
        tableName: null,
        fieldEn: null,
        fieldName: null,
        fieldType: null,
        fieldLength: null,
        point: null,
        isNull: null,
        isKey: null,
        defaultValue: null,
        remark: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (this.queryParams.tableEn == null) {
        this.$message.error("请选择表进行操作");
        return;
      }
      this.reset();
      this.open = true;
      this.title = "添加数据库-库字段";
      this.form.tableEn = this.queryParams.tableEn;
      this.form.tableName = this.queryParams.tableName;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDbfield(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据库-库字段";
      });
    },

    /** 修改表名按钮操作 */
    handleUpdate1(row) {
      const ids = row.id;
      this.reset();

      getDbtable(ids).then((response) => {
        this.form = response.data;
        this.opentable = true;
        this.title = "修改数据库-库";
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDbfield(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDbfield(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 提交表按钮 */
    submitTable() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDbtable(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.opentable = false;
              this.getList();
            });
          } else {
            addDbtable(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.opentable = false;
              this.getList();
            });
          }
        }
        location.reload()
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除数据库-库字段编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDbfield(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    /** 删除表名按钮操作 */
    handleDelete1(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除数据库-库编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDbtable(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.tableEn = data.label;
      // this.queryParams.tableName = data.comment;
      this.queryParams.pageNum = 1;
      this.getList();
    },
  },
};
</script>

  <style lang="scss" scoped>
.spanInfo {
  height: 36px;
  line-height: 36px;
}

.spanTitle {
  font-weight: 700;
  line-height: 36px;
  width: 120px;
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #606266;
  padding: 0 12px 0 0;
  box-sizing: border-box;
}

.demo-drawer__footer {
  margin: 0px 0px 20px 1220px;
}

.el-collapse-item__header {
  font-size: 20px;
  font-weight: bold;
  padding-left: 10px;
}

//   .app-container {
::v-deep .el-collapse {
  .el-collapse-item {
    .el-collapse-item__wrap {
      font-size: 16px;
    }
  }
}

.stepSucs {
  position: relative;
  height: 200px;
  padding-top: 100px;

  .box {
    position: absolute;
    left: 400px;
    bottom: 80px;
    width: 300px;
    height: 50px;
    background: #fff;
    border: 1px solid #e9ebf0;
    border-radius: 6px;
    margin: 5px 10px;
    z-index: 9999;
  }
}

.stepSuc :hover {
  cursor: pointer; //其他你想要的的css
}
::v-deep .el-collapse-item__header {
  font-size: 14px;
  font-weight: 700;
  color: #1b2132;
  // border: 1px solid #ebeef5;
  padding: 0 10px;
  border-radius: 10px;
  // margin: 10px 0;
}
::v-deep .el-collapse-item__wrap {
  border-bottom: none;
  padding-left: 10px;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-collapse-item:last-child {
  margin-bottom: 0;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
<style lang="scss" scoped>
.app-container-top {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  font-size: 20px;
  font-weight: 700;
  // padding: 0 10px;
  // margin-bottom: 20px;
}

::v-deep .el-form-item {
  display: flex;
  flex-direction: column;

  .el-form-item__content {
    margin-left: 10px !important;
  }

  .el-form-item__label {
    text-align: left !important;
    // line-height: 16px;
    // height: 16px;
    padding-left: 10px;
  }
}

::v-deep .el-collapse-item__content {
  border-radius: 10px;
}

::v-deep.el-descriptions-item {
  padding-bottom: 0px;
}
</style>