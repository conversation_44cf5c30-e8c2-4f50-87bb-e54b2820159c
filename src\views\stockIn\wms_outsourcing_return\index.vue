<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        style="margin-left: 5px"
        size="small"
        :inline="true"
        v-show="showSearch"
        :label-position="labelPosition"
        class="fromInputClass"
      >
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input
            v-model="queryParams.stockInNo"
            placeholder="请输入入库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商编号" prop="supplierCode">
          <el-input
            v-model="queryParams.supplierCode"
            placeholder="请输入供应商编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="stockInStateArr">
          <el-select
            multiple
            v-model="queryParams.stockInStateArr"
            placeholder="请选择入库状态"
          >
            <el-option
              v-for="dict in dict.type.stock_in_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 5px">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8" style="margin-left: 2px">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:wms_outsourcing_return:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:wms_outsourcing_return:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:wms_outsourcing_return:remove']">删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:wms_outsourcing_return:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-table
        v-loading="loading"
        :data="wms_outsourcing_returnList"
        @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="入库单号"
          align="center"
          prop="stockInNo"
          :width="
            tableWidth(wms_outsourcing_returnList.map((x) => x.stockInNo))
          "
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.stockInNo"
              >
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.stockInNo
                }}</span>
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.stockInNo"
                v-clipboard:success="onCopy"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="采购单号"
          width="170"
          align="center"
          prop="purchaseNo"
        />
        <el-table-column
          label="供应商编号"
          width="170"
          align="center"
          prop="supplierCode"
        />
        <el-table-column
          label="供应商名称"
          width="100"
          align="center"
          prop="supplierName"
        />

        <el-table-column label="入库状态" align="center" prop="stockInState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_in_state"
              :value="scope.row.stockInState"
            />
          </template>
        </el-table-column>
        <el-table-column
          width="260"
          label="操作"
          fixed="right"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-show="distroyCheck(scope.row)"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="updateType(scope.row, 'STOCK_PENDING')"
              v-hasPermi="['system:wms_outsourcing_return:edit']"
              >录入完成</el-button
            >
            <el-button
              v-show="distroyCheck(scope.row)"
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:wms_outsourcing_return:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
              @click="handleUpdate2(scope.row)"
              v-hasPermi="['system:wms_outsourcing_return:edit']"
              >详情</el-button
            >
            <el-button
              v-show="distroyCheck(scope.row)"
              size="mini"
              type="text"
              icon="el-icon-delete-solid"
              @click="updateType(scope.row, 'HAVE_BEEN')"
              v-hasPermi="['system:stock_in:remove']"
              >作废</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 添加或修改委外退货对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'70%'"
        append-to-body
      >
        <el-form
          :label-position="labelPosition"
          label-width="100px"
          ref="form"
          :model="form"
          :rules="rules"
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item title="委外退货单信息" name="1">
              <el-row :gutter="24" class="mb8">
                <el-col :span="7">
                  <el-form-item label="入库类型" prop="stockInType">
                    <el-select
                      disabled
                      v-model="form.stockInType"
                      placeholder="请选择入库类型"
                    >
                      <el-option
                        v-for="dict in dict.type.stock_in_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="采购单号" prop="purchaseNo" style="width: 320px">
                    <!-- 添加 ref 用于主动失焦 -->
                    <el-input
                      @focus="importFuction"
                      v-model="form.purchaseNo"
                      placeholder="请输入·采购单号"
                      ref="purchaseInput"
                      :disabled="isPurchaseOrderDisabled"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="供应商编码" prop="supplierCode">
                    <el-input
                      v-model="form.supplierCode"
                      placeholder="请输入供应商编码"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24" class="mb8">
                <el-col :span="7">
                  <el-form-item label="供应商名称" prop="supplierName">
                    <el-input
                      v-model="form.supplierName"
                      placeholder="请输入供应商名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="备注内容" prop="remark">
                    <el-input
                      v-model="form.remark"
                      placeholder="请输入备注内容"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
            <!-- <el-divider content-position="center" style="width: 98%"
              >入库单明细信息</el-divider
            > -->
            <el-collapse-item title="委外退货单明细" name="2">
              <div style="margin: 10px">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="small"
                  @click="handleAddWmsStockInDetail"
                  >添加</el-button
                >
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="small"
                  @click="handleDeleteWmsStockInDetail"
                  >删除</el-button
                >
              </div>
              <el-form
                :model="stockInDetailform"
                ref="stockInDetailform"
                :rules="stockInDetailform.rules"
              >
                <el-table
                  :data="stockInDetailform.wmsStockInDetailList"
                  :row-class-name="rowWmsStockInDetailIndex"
                  @selection-change="handleWmsStockInDetailSelectionChange"
                  ref="wmsStockInDetail"
                  :cell-style="{ padding: '1px 0' }"
                >
                  <el-table-column type="selection" width="50" align="center" />

                  <el-table-column label="物料编码" prop="materialCode">
                    <!-- <template slot-scope="scope">
                  <el-input v-model="scope.row.materialCode" placeholder="请输入物料编码" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="物料名称" prop="materialName">
                    <!-- <template slot-scope="scope">
                  <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="规格型号" prop="specification">
                    <!-- <template slot-scope="scope">
                  <el-input v-model="scope.row.specification" placeholder="请输入规格型号" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="单位" prop="materialUnit">
                    <!-- <template slot-scope="scope">
                  <el-input v-model="scope.row.materialUnit" placeholder="请输入单位" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="数量" prop="qty">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'wmsStockInDetailList.' + scope.$index + '.qty'"
                        style="display: inline-grid"
                        :rules="stockInDetailform.rules.qty"
                      >
                        <el-input
                          size="small"
                          v-model="scope.row.qty"
                          placeholder="请输入数量"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div style="display: flex; justify-content: end; margin: 10px">
          <el-button size="small" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button size="small" @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <el-drawer
        :title="title2"
        :visible.sync="open2"
        :size="'75%'"
        append-to-body
      >
        <el-form
          ref="form"
          :model="form"
          size="small"
          :label-position="labelPosition"
          :inline="true"
          label-width="90px"
          ><el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="委外退货单" name="1">
              <el-form-item label="入库单号" prop="stockInNo">
                <el-input disabled v-model="form.stockInNo" placeholder="" />
              </el-form-item>
              <el-form-item label="采购单号" prop="purchaseNo">
                <el-input disabled v-model="form.purchaseNo" placeholder="" />
              </el-form-item>
              <el-form-item label="收货单id" prop="reviceId">
                <el-input disabled v-model="form.reviceId" placeholder="" />
              </el-form-item>
              <el-form-item label="供应商编码" prop="supplierCode">
                <el-input disabled v-model="form.supplierCode" placeholder="" />
              </el-form-item>
              <el-form-item label="供应商名称" prop="supplierName">
                <el-input disabled v-model="form.supplierName" placeholder="" />
              </el-form-item>
              <el-form-item label="入库日期" prop="stockInDate">
                <el-date-picker
                  disabled
                  clearable
                  v-model="form.stockInDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder=""
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="入库状态" prop="stockInState">
                <el-select disabled v-model="form.stockInState" placeholder="">
                  <el-option
                    v-for="dict in dict.type.stock_in_state"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="委外退货明细" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="入库明细" name="first">
                  <StockDetail
                    :stock_in_id="stock_in_id"
                    :key="refreshValue"
                    :activeName="activeName"
                  />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <StockDetail
                        @sendDetailId="receiveDetailData"
                        :stock_in_id="stock_in_id"
                        :key="refreshValue"
                        :activeName="activeName"
                      />
                    </div>
                    <div style="width: 48%">
                      <StockBox
                        :stock_detail_id="stock_detail_id"
                        :key="stock_detail_id"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
      <!-- 明细添加物料对话框 -->
      <MaterialDialog
        :materialDialogVisible="materialDialogVisible"
        @updateDialogVisible="updateDialogVisible"
        @selectMateriaList="selectMateriaList"
      >
      </MaterialDialog>

            <!-- 采购单抽屉 -->
      <el-drawer
        :title="purchaseTitle"
        :visible.sync="purchaseDrawerVisible"
        :size="'60%'"
        append-to-body
        @close="handleDrawerClose"
      >
        <!-- 使用栅格系统实现左右分栏 -->
        <el-row :gutter="20" style="height: 100%">
          <!-- 左侧表单 -->
          <el-col :span="12">
            <el-form
              ref="leftForm"
              :model="purchaseQueryParams"
              size="small"
              label-width="100px"
              class="left-form"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="7">
                  <el-form-item label="采购单号">
                    <el-input v-model="purchaseQueryParams.purchaseNo" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="供应商编码">
                    <el-input v-model="purchaseQueryParams.supplierCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="供应商名称">
                    <el-input v-model="purchaseQueryParams.supplierName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handlePurchaseQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetPurchaseQuery"
                      >重置</el-button
                    >
                  </el-form-item></el-col
                >
              </el-row>
              <el-row :gutter="10" class="mb8"></el-row>
              <!-- 更多左侧表单项... -->
              <el-table
                :data="purchaseList"
                highlight-current-row
                style="cursor: pointer"
                v-loading="purchaseLoading"
                height="40vh"
                @selection-change="handleSelectionChangePurchase"
                @select-all="onSelectAll"
                @row-click="rowClick"
                ref="leftMultipleTable"
              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column type="index" width="55" align="center" />
                <el-table-column label="采购单号" prop="purchaseNo">
                </el-table-column>
                <el-table-column label="供应商编码" prop="supplierCode">
                </el-table-column>
                <el-table-column label="供应商名称" prop="supplierName">
                </el-table-column>
              </el-table>
              <pagination
                v-show="purchasetotal > 0"
                :total="purchasetotal"
                :page.sync="purchaseQueryParams.pageNum"
                :limit.sync="purchaseQueryParams.pageSize"
                @pagination="loadPurchase"
              />
            </el-form>
          </el-col>

          <!-- 右侧表单 -->
          <el-col :span="12">
            <el-form
              ref="rightForm"
              :model="purchaseQueryDetailParams"
              size="small"
              label-width="100px"
              class="right-form"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item label="物料编号">
                    <el-input v-model="purchaseQueryDetailParams.partCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="物料名称">
                    <el-input v-model="purchaseQueryDetailParams.partName" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handlePurchaseDetailQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetPurchaseDetailQuery"
                      >重置</el-button
                    >
                  </el-form-item></el-col
                >
              </el-row>
              <!-- 更多右侧表单项... -->
              <el-table
                :data="purchaseDetailList"
                @selection-change="handleSelectionDetail"
                ref="rightMultipleTable"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="物料编号" prop="partCode">
                </el-table-column>
                <el-table-column label="物料名称" prop="partName">
                </el-table-column>
              </el-table>
              <pagination
                v-show="purchaseDetailtotal > 0"
                :total="purchaseDetailtotal"
                :page.sync="purchaseQueryDetailParams.pageNum"
                :limit.sync="purchaseQueryDetailParams.pageSize"
                @pagination="loadPurchaseDetail"
              />
            </el-form>
          </el-col>
        </el-row>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="handleAddPurchaseDetail"
            >保 存</el-button
          >
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>
<script>
import {
  listWms_outsourcing_return,
  getWms_outsourcing_return,
  delWms_outsourcing_return,
  addWms_outsourcing_return,
  updateWms_outsourcing_return,
} from "@/api/system/wms_outsourcing_return";
import MaterialDialog from "../componet/materialDialog.vue";
import StockDetail from "../stock_detail/componet.vue";
import StockBox from "../stock_box/stockBoxComponet.vue";
import { listWarehouse } from "@/api/system/warehouse";
import { listPurchase_detail } from "@/api/system/purchase_detail";
import {
  listPurchase,
  getPurchase,
  delPurchase,
  addPurchase,
  updatePurchase,
} from "@/api/system/purchase";
export default {
  name: "wms_outsourcing_return",
  dicts: ["stock_in_type", "stock_in_state", "quality_state"],
  components: {
    MaterialDialog,
    StockDetail,
    StockBox, // 注册子组件
  },
  data() {
    return {
      activeNames: ["1", "2"],
      //详情
      activeNamesInfo: ["1", "2"],
      // 用于存储所有已存在的 id
      existingIds: new Set(),
      //控制物料弹出框组件的属性
      materialDialogVisible: false,
      isPurchaseOrderDisabled: false,
      // 入库单明细表格数据
      stockInDetailform: {
        wmsStockInDetailList: [],
        rules: {
          qty: [
            { required: true, message: "数量不能为空", trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                const num = Number(value);
                if (isNaN(num) || num <= 0) {
                  callback(new Error("数量必须大于0"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
      },
      // 刷新子组件的key值
      refreshValue: -1,
      open2: false,
      title2: "",
      // 表单查询时候的展示更多条件的boolean
      moreSelect: false,
      //传给箱表的id 来源于detail组件
      stock_detail_id: "",
      flushState: true,
      // 主表具体的id
      stock_in_id: "",
      //tabs标签的name
      activeName: "first",
      labelPosition: "right",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockInDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 委外退货表格数据
      wms_outsourcing_returnList: [],
      //选中的明细
      selectedPurchaseDetailList: [],
      // 明细采购单明细
      purchaseDetailList: [],
      purchaseDetailtotal: 0,
      purchaseInputValue: "",
      purchaseDetailLoading: false,
      // 采购单抽屉弹窗
      purchaseTitle: "",
      // 控制采购单抽屉
      purchaseDrawerVisible: false,
      // 采购单表单数据
      purchaseList: [],
      purchasetotal: 0,
      purchaseLoading: false,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "ISSUE",
        stockInDate: null,
        stockInState: null,
        stockInStateArr: [],
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        comId: null,
      },
      purchaseQueryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierCode: "",
        supplierName: "",
        purchaseNo: "",
        purchaseNo: "",
      },
      purchaseQueryDetailParams: {
        pageNum: 1,
        pageSize: 10,
        partCode: "",
        partName: "",
      },
      //仓库列表
      warehouseList: [],
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        purchaseNo: [
          { required: true, message: "单号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadPurchase();
    this.loadPurchaseDetail();
    this.ckList();
  },
  methods: {
    //明细push子组件传过来的数据（选中的物料）
    selectMateriaList(arrs) {
      console.log("子组件传过来的物料信息", arrs);

      // 1. 过滤掉已存在的 id（首次传入时 existingIds 为空，所有数据都会通过）
      const uniqueNewItems = arrs.filter((item) => {
        if (this.existingIds.has(item.id)) {
          console.log("跳过重复项:", item);
          return false;
        }
        return true;
      });
      // 2. 将去重后的数据推入父组件列表
      this.stockInDetailform.wmsStockInDetailList.push(...uniqueNewItems);
      // 3. 将新数据的 id 添加到全局去重集合（确保下次能检测到重复）
      uniqueNewItems.forEach((item) => {
        this.existingIds.add(item.id);
      });
      console.log(
        "更新后的明细列表:",
        this.stockInDetailform.wmsStockInDetailList
      );
      console.log("已存在的 id 集合:", this.existingIds);
      this.updateDialogVisible();
      // this.stockInDetailform.wmsStockInDetailList.push(...arrs);
      // this.updateDialogVisible()
    },
    //让父组件关闭子组件的弹出框
    updateDialogVisible(val) {
      this.materialDialogVisible = false;
    },

    /** 入库单明细添加按钮操作 */
    handleAddWmsStockInDetail() {
      this.materialDialogVisible = true;
    },
    //采购明细搜索按钮
    handlePurchaseQuery() {
      this.purchaseQueryParams.pageNum = 1;
      this.purchaseQueryParams.pageNum = 10;
      this.loadPurchase();
    },
    //采购明细重置按钮
    resetPurchaseQuery() {
      (this.purchaseQueryParams.purchaseNo = ""),
        (this.purchaseQueryParams.supplierCode = ""),
        (this.purchaseQueryParams.supplierName = ""),
        (this.purchaseQueryParams.purchaseNo = ""),
        this.handlePurchaseQuery();
    },
        handleClose() {
      // 重置一下左边勾选
      this.$refs?.leftMultipleTable?.clearSelection();
      this.purchaseDrawerVisible = false;
    },
    handleSelectionDetail(selection) {
      this.selectedPurchaseDetailList = selection;
      this.selectedItems = selection; // 直接保存完整数据对象
      // this.ids = selection.map((item) => item.id);

      // // 根据ID筛选需要提交的数据

      // this.selectedPurchaseDetailList = this.purchaseDetailList.filter((item) =>
      //   this.ids.includes(item.id)
      // );

      // 更新选择状态
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handlePurchaseDetailQuery() {
      // 物料搜索点击
      this.purchaseQueryDetailParams.pageNum = 1;
      listPurchase_detail(this.purchaseQueryDetailParams).then((response) => {
        this.purchaseDetailList = response.rows;
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    resetPurchaseDetailQuery() {
      (this.purchaseQueryParams.purchaseNo = ""),
        (this.purchaseQueryDetailParams.partCode = ""),
        (this.purchaseQueryDetailParams.partName = ""),
        listPurchase_detail(this.purchaseQueryParams).then((response) => {
          this.purchaseDetailList = response.rows;

          this.purchaseDetailtotal = response.total;
          this.purchaseDetailLoading = false;
        });
    },
    //仓库数组选中改变的时候
    ckChange(val) {
      const item = this.warehouseList.find((item) => item.id === val);
      if (item) {
        this.form.warehouseCode = item.warehouseCode;
        this.form.warehouseId = item.id;
        this.form.warehouseName = item.warehouseName;
      }
    },
    /** 查询仓库列表 */
    ckList() {
      let qur = {
        pageNum: 1,
        pageSize: 1000,
        warehouseCode: null,
        warehouseName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
      };
      listWarehouse(qur).then((response) => {
        this.warehouseList = response.rows;
      });
    },
    // 子传父 方法处理
    receiveDetailData(data) {
      this.stock_detail_id = data;
    },
    handleClick() {
      if (this.activeName == "first") {
        this.stock_detail_id = null;
      }
    },
    /** 修改按钮操作 */
    handleUpdate2(row, type) {
      this.refreshValue += 1;
      this.reset();
      this.stock_in_id = row.id;
      const id = row.id || this.ids;
      getWms_outsourcing_return(id).then((response) => {
        this.form = response.data;
        this.stockInDetailform.wmsStockInDetailList =
          response.data.wmsStockInDetailList;
        this.open2 = true;
        this.title2 = "委外退货详情";
      });
    },
    //作废或者完成录入的时候的更新方法
    updateType(row, type) {
      var content = "";
      content = type == "HAVE_BEEN" ? "作废，" : "录入完成，";
      this.$modal
        .confirm(
          "是否" + content + '入库单号为"' + row.stockInNo + '"的数据项？'
        )
        .then(function () {})
        .then(() => {
          getWms_outsourcing_return(row.id).then((response) => {
            this.form = response.data;
            this.form.stockInState = type;
            updateWms_outsourcing_return(this.form).then((response) => {
              this.getList();
              this.$modal.msgSuccess(content + "成功");
            });
          });
        })
        .catch(() => {});
    },
    handleDrawerClose() {
      // ​​焦点管理​​
      // 打开抽屉时强制输入框失焦，避免遮罩层无法点击。
      // 关闭抽屉时再次触发失焦，确保状态干净

      this.purchaseDrawerVisible = false;
      this.$refs.purchaseInput?.blur(); // 主动触发失焦
    },
    //判别销毁或者作废按钮应不应该出来的方法
    distroyCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "PUT_STORAGE":
          return false;
        case "BE_IN_STORAGE":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        default:
          return true;
      }
    },
    // 判断关闭按钮是否应该出来
    closeCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "CLOSED":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        case "RETURNED":
          return false;
        default:
          return true;
      }
    },
    /** 查询委外退货列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: "createTime",
        isAsc: "desc",
      };
      listWms_outsourcing_return(this.queryParams).then((response) => {
        this.wms_outsourcing_returnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      // 取消的时候，清除保存的existingIds
      this.existingIds = new Set();
      console.log("取消后的 existingIds:", this.existingIds);
      this.open = false;
      this.reset();
    },
    handleAddPurchaseDetail() {
      // 这里根据输入去判断是否为空，如果有数据就是修改
      if (this.purchaseInputValue !== "") {
        console.log("purchaseInputValue:", this.purchaseInputValue);
        this.stockInDetailform.wmsStockInDetailList = [];
        console.log(
          "去修改的时候先清空this.form.wmsStockInDetailList:",
          this.stockInDetailform.wmsStockInDetailList
        );
      }

      this.selectedPurchaseDetailList.forEach((obj) => {
        // 检查当前 obj 的 id 是否已存在
        // 如果存在，就不去添加
        if (!this.existingIds.has(obj.id)) {
          // 赋值字段
          obj.materialId = obj.id;
          obj.materialCode = obj.partCode;
          obj.materialName = obj.partName;
          obj.specification = obj.partSpecification;
          obj.materialUnit = obj.uom;
          console.log(obj, "obj---------");
          // 推送数据到目标数组
          this.stockInDetailform.wmsStockInDetailList.push(obj);
          console.log(
            this.stockInDetailform.wmsStockInDetailList,
            "this.stockInDetailform.wmsStockInDetailList"
          );
          // 标记该 id 已存在
          this.existingIds.add(obj.id);
        }
      });
      this.purchaseDrawerVisible = false;
      // 去disabled采购订单框
      this.isPurchaseOrderDisabled = this.stockInDetailform.wmsStockInDetailList.length > 0;

      this.$refs?.leftMultipleTable?.clearSelection();
      this.$refs?.rightMultipleTable?.clearSelection();
    },
      importFuction(event) {
      // 重置采购单明细搜索的数据
      this.purchaseDetailList = [];
      this.purchaseDetailtotal = 0;
      // 获取采购单，用于后面判断
      this.purchaseInputValue = event.target.value; // 直接获取输入框的当前值
      console.log("purchaseInputValue输入框的值:", this.purchaseInputValue);

      event.target.blur(); // 输入框失焦后再打开抽屉
      this.purchaseDrawerVisible = true;
      this.purchaseTitle = "选择采购订单";
    },
    // 多选框选中数据
    handleSelectionChangePurchase(selection) {
      let purchaseNo;
      let newArr = [];
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.leftMultipleTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.leftMultipleTable.toggleRowSelection(it, false);
            return false;
          }
        });
        newArr = newRows;
        purchaseNo = newRows[0].id;
      } else {
        newArr = selection;
        purchaseNo = selection[0].id;
      }
      this.form.purchaseNo = newArr[0].purchaseNo;
      this.form.purchaseNo = newArr[0].id;
      this.form.supplierId = newArr[0].supplierId;
      this.form.supplierCode = newArr[0].supplierCode;
      this.form.supplierName = newArr[0].supplierName;
      const params = {
        purchaseNo: this.form.purchaseNo,
      };

      listPurchase_detail(params).then((response) => {
        this.purchaseDetailList = response.rows;

        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    // 加载采购单
    loadPurchase() {
      this.purchaseLoading = true;
      listPurchase(this.purchaseQueryParams).then((response) => {
        this.purchaseList = response.rows;
        this.purchasetotal = response.total;
        this.purchaseLoading = false;
      });
    },
    // 加载采购单明细
    loadPurchaseDetail() {
      this.purchaseDetailLoading = true;
      listPurchase_detail(this.purchaseQueryParams).then((response) => {
        this.purchaseDetailList = response.rows;
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    rowClick(row) {
      this.$refs.leftMultipleTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    onSelectAll() {
      this.$refs.leftMultipleTable.clearSelection();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "ISSUE",
        stockInDate: null,
        stockInState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.stockInDetailform.wmsStockInDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 重置采购订单输入框的禁用状态
      this.isPurchaseOrderDisabled = false;
      // 清空已存在的ID集合
      this.existingIds = new Set();
      // 重置采购单号输入值
      this.purchaseInputValue = "";
      this.open = true;
      this.title = "添加委外退货单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWms_outsourcing_return(id).then((response) => {
        this.form = response.data;
        this.stockInDetailform.wmsStockInDetailList =
          response.data.wmsStockInDetailList;
        this.open = true;
        this.title = "修改委外退货";
      });
    },
    /** 提交按钮 */

    submitForm() {
      this.$refs["stockInDetailform"].validate((vd) => {
        if (vd) {
          if (this.stockInDetailform.wmsStockInDetailList.length === 0) {
            this.$message.error("至少需要添加一条明细！");
            return;
          }
          this.$refs["form"].validate((valid) => {
            if (valid) {
              this.form.wmsStockInDetailList =
                this.stockInDetailform.wmsStockInDetailList;
              if (this.form.id != null) {
                updateWms_outsourcing_return(this.form).then((response) => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                addWms_outsourcing_return(this.form).then((response) => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                });
              }
            }
          });
        } else {
          console.log("不通过");
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除委外退货编号为"' + ids + '"的数据项？')
        .then(function () {
          return delWms_outsourcing_return(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 入库单明细序号 */
    rowWmsStockInDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },

    /** 入库单明细删除按钮操作 */
    handleDeleteWmsStockInDetail() {
      if (this.checkedWmsStockInDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单明细数据");
      } else {
        const wmsStockInDetailList =
          this.form.wmsStockInDetailList;
        const checkedWmsStockInDetail = this.checkedWmsStockInDetail;

        // 1. 收集被删除项的 ID（根据选中的 index）
        const deletedIds = checkedWmsStockInDetail
          .map((index) => wmsStockInDetailList[index - 1]?.id) // 根据 index 获取 id index的要从0开始，所以这里要-1！！
          .filter((id) => id !== undefined); // 过滤无效值
        console.log("被删除的 ID:", deletedIds);

        this.stockInDetailform.wmsStockInDetailList =
          wmsStockInDetailList.filter(function (item) {
            return checkedWmsStockInDetail.indexOf(item.index) == -1;
          });
        // 这里是Set
        deletedIds.forEach((id) => this.existingIds.delete(id));
        console.log("更新后的 existingIds:", this.existingIds);
      }
    },
    /** 复选框选中数据 */
    handleWmsStockInDetailSelectionChange(selection) {
      this.checkedWmsStockInDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/wms_outsourcing_return/export",
        {
          ...this.queryParams,
        },
        `委外退货_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  padding: 8px 0 !important;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}
</style>