<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" :label-position="labelPosition" class="fromInputClass"
        label-width="80px" size="small" :inline="true" v-show="showSearch">
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input v-model="queryParams.stockInNo" placeholder="请输入入库单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="工单编码" prop="workerNo">
          <el-input v-model="queryParams.workerNo" placeholder="请输入工单编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="stockInStateArr">
          <el-select multiple v-model="queryParams.stockInStateArr" placeholder="请选择入库状态">
            <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 20px">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8" style="margin-left: 2px">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:production_receipt:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:production_receipt:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-table v-loading="loading" :data="production_receiptList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="入库单号" align="center" prop="stockInNo"
          :width="tableWidth(production_receiptList.map((x) => x.stockInNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip placement="top" effect="dark" :content="scope.row.stockInNo">
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.stockInNo
                  }}</span>
              </el-tooltip>
              <i style="margin-left: 10px; cursor: pointer" class="el-icon-document-copy"
                v-clipboard:copy="scope.row.stockInNo" v-clipboard:success="onCopy"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="工单编码" align="center" width="200" prop="workerNo" />
        <!-- <el-table-column label="物料编码" align="center" prop="materialCode" /> -->
        <!-- <el-table-column label="物料名称" align="center" prop="materialName" /> -->
        <el-table-column label="状态" align="center" prop="stockInState">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.stock_in_state" :value="scope.row.stockInState" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-show="distroyCheck(scope.row)" size="mini" 
            type="text" icon="el-icon-check"
              @click="updateType(scope.row, 'STOCK_PENDING')"
              v-hasPermi="['system:production_receipt:edit']">录入完成</el-button>

            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-edit-outline"
              @click="handleUpdate(scope.row)" v-hasPermi="['system:production_receipt:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-s-order" @click="handleUpdate2(scope.row)"
              v-hasPermi="['system:production_receipt:edit']">详情</el-button>

            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-delete-solid"
              @click="updateType(scope.row, 'HAVE_BEEN')" v-hasPermi="['system:stock_in:remove']">作废</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
      <!-- 添加或修改生产入库对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'70%'" append-to-body>
        <el-form :label-position="labelPosition" label-width="80px" size="default" ref="form" :model="form"
          :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="入库信息" name="1">
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="入库类型" prop="stockInType">
                    <el-select disabled v-model="form.stockInType" placeholder="请选择入库类型">
                      <el-option v-for="dict in dict.type.stock_in_type" :key="dict.value" :label="dict.label"
                        :value="dict.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="仓库名称" prop="warehouseId">
                    <el-select @change="ckChange" v-model="form.warehouseName" placeholder="请选择仓库">
                      <el-option v-for="(item, index) in warehouseList" :key="index" :label="item.warehouseName"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" placeholder="请输入内容" />
                  </el-form-item>
                </el-col> </el-row></el-collapse-item>
            <!-- <div class="title_style_div">入库信息</div> -->

            <!-- <div class="borderLeftStyle">工单信息</div> -->

            <!-- <el-row :gutter="24">

            <el-col :span="7">
              <el-form-item label="工单编码" prop="workerNo">
                <el-input v-model="form.workerNo" placeholder="请输入内容工单编码" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="工单类型" prop="workerType">
                <el-input v-model="form.workerType" placeholder="请输入工单类型" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="物料名称" prop="materialName">
                <el-input v-model="form.materialName" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="数量" prop="qty">
                <el-input v-model="form.qty" placeholder="请输入物料数量" />
              </el-form-item>
            </el-col>
          </el-row> -->

            <!-- <div class="title_style_div">入库单明细信息</div> -->
            <el-collapse-item title="入库单明细信息" name="2">
              <div style="margin-left: 10px">
                <el-button type="primary" icon="el-icon-plus" size="mini"
                  @click="handleAddWmsStockInDetail">添加</el-button>
                <el-button type="danger" icon="el-icon-delete" size="mini"
                  @click="handleDeleteWmsStockInDetail">删除</el-button>
              </div>
              <el-form :model="stockInDetailform" ref="stockInDetailform" :rules="stockInDetailform.rules">
                <el-table :data="stockInDetailform.wmsStockInDetailList" :row-class-name="rowWmsStockInDetailIndex"
                  @selection-change="handleWmsStockInDetailSelectionChange" ref="wmsStockInDetail" with="100%">
                  <el-table-column type="selection" width="50" align="center" />
                  <el-table-column label="物料编码" prop="materialCode" :width="tableWidth(
                    stockInDetailform.wmsStockInDetailList.map(
                      (x) => x.materialCode
                    )
                  )
                    ">
                    <!-- <template slot-scope="scope">
                  <el-input size="small" v-model="scope.row.materialCode" placeholder="请输入物料编码" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="物料名称" prop="materialName" :width="tableWidth(
                    stockInDetailform.wmsStockInDetailList.map(
                      (x) => x.materialName
                    )
                  )
                    ">
                    <!-- <template slot-scope="scope">
                  <el-input size="small" v-model="scope.row.materialName" placeholder="请输入物料名称" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="规格型号" prop="specification">
                    <!-- <template slot-scope="scope">
                  <el-input size="small" v-model="scope.row.specification" placeholder="请输入规格型号" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="单位" prop="materialUnit">
                    <!-- <template slot-scope="scope">
                  <el-input size="small" v-model="scope.row.materialUnit" placeholder="请输入单位" />
                </template> -->
                  </el-table-column>
                  <el-table-column label="数量" prop="qty">
                    <template slot-scope="scope">
                      <el-form-item :prop="'wmsStockInDetailList.' + scope.$index + '.qty'" style="display: inline-grid"
                        :rules="stockInDetailform.rules.qty">
                        <el-input size="small" v-model="scope.row.qty" placeholder="请输入数量" />
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div style="margin: 5px; display: flex; justify-content: end">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 详情抽屉 -->
      <el-drawer :title="title2" :visible.sync="open2" :size="'75%'" append-to-body>
        <el-form ref="form" :model="form" size="small" :label-position="labelPosition" class="" label-width="90px"
          :inline="true">
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="生产入库单信息" name="1">
              <el-form-item label="入库单号" prop="stockInNo">
                <el-input disabled v-model="form.stockInNo" placeholder="" />
              </el-form-item>
              <el-form-item label="采购单号" prop="purchaseNo">
                <el-input disabled v-model="form.purchaseNo" placeholder="" />
              </el-form-item>
              <el-form-item label="收货单id" prop="reviceId">
                <el-input disabled v-model="form.reviceId" placeholder="" />
              </el-form-item>
              <el-form-item label="供应商编码" prop="supplierCode">
                <el-input disabled v-model="form.supplierCode" placeholder="" />
              </el-form-item>
              <el-form-item label="供应商名称" prop="supplierName">
                <el-input disabled v-model="form.supplierName" placeholder="" />
              </el-form-item>
              <el-form-item label="入库日期" prop="stockInDate">
                <el-date-picker disabled clearable v-model="form.stockInDate" type="date" value-format="yyyy-MM-dd"
                  placeholder="">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="入库状态" prop="stockInState">
                <el-select disabled v-model="form.stockInState" placeholder="">
                  <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="生产入库单明细" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="入库明细" name="first">
                  <StockDetail :stock_in_id="stock_in_id" :key="refreshValue" :activeName="activeName" />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <StockDetail @sendDetailId="receiveDetailData" :stock_in_id="stock_in_id" :key="refreshValue"
                        :activeName="activeName" />
                    </div>
                    <div style="width: 48%">
                      <StockBox :stock_detail_id="stock_detail_id" :key="stock_detail_id" />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>

      <!-- 明细添加物料对话框 -->
      <MaterialDialog :materialDialogVisible="materialDialogVisible" @updateDialogVisible="updateDialogVisible"
        @selectMateriaList="selectMateriaList">
      </MaterialDialog>
    </div>
  </div>
</template>
<script>
import {
  listProduction_receipt,
  getProduction_receipt,
  delProduction_receipt,
  addProduction_receipt,
  updateProduction_receipt,
} from "@/api/system/production_receipt";
import StockDetail from "../stock_detail/componet.vue";
import StockBox from "../stock_box/stockBoxComponet.vue";
import MaterialDialog from "../componet/materialDialog.vue";
import { listWarehouse } from "@/api/system/warehouse";
export default {
  name: "production_receipt",
  dicts: ["stock_in_type", "stock_in_state", "quality_state"],
  components: {
    StockDetail,
    StockBox,
    MaterialDialog,
    // 注册子组件
  },
  data() {
    return {
      activeNames: ["1", "2"],
      //详情
      activeNamesInfo: ["1", "2"],
      // 用于存储所有已存在的 id
      existingIds: new Set(),
      //控制物料弹出框组件的属性
      materialDialogVisible: false,
      // 刷新子组件的key值
      refreshValue: -1,
      open2: false,
      title2: "",
      // 表单查询时候的展示更多条件的boolean
      moreSelect: false,
      //传给箱表的id 来源于detail组件
      stock_detail_id: "",
      flushState: true,
      // 主表具体的id
      stock_in_id: "",
      //tabs标签的name
      activeName: "first",
      labelPosition: "right",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockInDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生产入库表格数据
      production_receiptList: [],
      // 入库单明细表格数据
      stockInDetailform: {
        wmsStockInDetailList: [],
        rules: {
          qty: [
            { required: true, message: "数量不能为空", trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                const num = Number(value);
                if (isNaN(num) || num <= 0) {
                  callback(new Error("数量必须大于0"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "PRODUCE",
        stockInDate: null,
        stockInState: null,
        stockInStateArr: [],
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        comId: null,
        workerNo: null,
      },
      //仓库列表
      warehouseList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // workerNo: [
        //   { required: true, message: "工单编码不能为空", trigger: "blur" }
        // ],
        // 添加一个虚拟字段用于校验数组长度
      },
    };
  },
  created() {
    this.getList();
    this.ckList();
  },
  methods: {
    //明细push子组件传过来的数据（选中的物料）
    selectMateriaList(arrs) {
      console.log("子组件传过来的物料信息", arrs);

      // 1. 过滤掉已存在的 id（首次传入时 existingIds 为空，所有数据都会通过）
      const uniqueNewItems = arrs.filter((item) => {
        if (this.existingIds.has(item.id)) {
          console.log("跳过重复项:", item);
          return false;
        }
        return true;
      });
      // 2. 将去重后的数据推入父组件列表
      this.stockInDetailform.wmsStockInDetailList.push(...uniqueNewItems);
      // 3. 将新数据的 id 添加到全局去重集合（确保下次能检测到重复）
      uniqueNewItems.forEach((item) => {
        this.existingIds.add(item.id);
      });
      console.log(
        "更新后的明细列表:",
        this.stockInDetailform.wmsStockInDetailList
      );
      console.log("已存在的 id 集合:", this.existingIds);
      this.updateDialogVisible();
    },
    //让父组件关闭子组件的弹出框
    updateDialogVisible(val) {
      this.materialDialogVisible = false;
    },
    //仓库数组选中改变的时候
    ckChange(val) {
      const item = this.warehouseList.find((item) => item.id === val);
      if (item) {
        this.form.warehouseCode = item.warehouseCode;
        this.form.warehouseId = item.id;
        this.form.warehouseName = item.warehouseName;
      }
    },
    /** 查询仓库列表 */
    ckList() {
      let qur = {
        pageNum: 1,
        pageSize: 1000,
        warehouseCode: null,
        warehouseName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
      };
      listWarehouse(qur).then((response) => {
        this.warehouseList = response.rows;
      });
    },
    // 子传父 方法处理
    receiveDetailData(data) {
      this.stock_detail_id = data;
    },
    handleClick() {
      if (this.activeName == "first") {
        this.stock_detail_id = null;
      }
    },
    /** 修改按钮操作 */
    handleUpdate2(row, type) {
      this.refreshValue += 1;
      this.reset();
      this.stock_in_id = row.id;
      const id = row.id || this.ids;
      getProduction_receipt(id).then((response) => {
        this.form = response.data;
        this.stockInDetailform.wmsStockInDetailList =
          response.data.wmsStockInDetailList;
        this.open2 = true;
        this.title2 = "生产入库单详情";
      });
    },
    //作废或者完成录入的时候的更新方法
    updateType(row, type) {
      var content = "";

      content = type == "HAVE_BEEN" ? "作废，" : "录入完成，";
      this.$modal
        .confirm(
          "是否" + content + '入库单号为"' + row.stockInNo + '"的数据项？'
        )
        .then(function () { })
        .then(() => {
          getProduction_receipt(row.id).then((response) => {
            this.form = response.data;
            this.form.stockInState = type;
            updateProduction_receipt(this.form).then((response) => {
              this.getList();
              this.$modal.msgSuccess(content + "成功");
            });
          });
        })
        .catch(() => { });
    },
    //判别销毁或者作废按钮应不应该出来的方法
    distroyCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "PUT_STORAGE":
          return false;
        case "BE_IN_STORAGE":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        default:
          return true;
      }
    },
    // 判断关闭按钮是否应该出来
    closeCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "CLOSED":
          return false;
        case "HAVE_BEEN":
          return false;
        case "STOCK_PENDING":
          return false;
        case "RETURNED":
          return false;
        default:
          return true;
      }
    },
    /** 查询生产入库列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: "createTime",
        isAsc: "desc",
      };
      listProduction_receipt(this.queryParams).then((response) => {
        this.production_receiptList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      // 取消的时候，清除保存的existingIds
      this.existingIds = new Set();
      console.log("取消后的 existingIds:", this.existingIds);

      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: "PRODUCE",
        stockInDate: null,
        stockInState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        workerNo: null,
        workerType: null,
        qty: null,
      };
      this.stockInDetailform.wmsStockInDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增生产入库单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProduction_receipt(id).then((response) => {
        this.form = response.data;
        this.stockInDetailform.wmsStockInDetailList =
          response.data.wmsStockInDetailList;
        this.open = true;
        this.title = "修改生产入库单";
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["stockInDetailform"].validate((vd) => {
        if (vd) {
          if (this.stockInDetailform.wmsStockInDetailList.length === 0) {
            this.$message.error("至少需要添加一条明细！");
            return;
          }
          this.$refs["form"].validate((valid) => {
            if (valid) {
              this.form.wmsStockInDetailList =
                this.stockInDetailform.wmsStockInDetailList;
              if (this.form.id != null) {
                updateProduction_receipt(this.form).then((response) => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                addProduction_receipt(this.form).then((response) => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                });
              }
            }
          });
        } else {
          console.log("不通过");
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除生产入库编号为"' + ids + '"的数据项？')
        .then(function () {
          return delProduction_receipt(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 入库单明细序号 */
    rowWmsStockInDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 入库单明细添加按钮操作 */
    handleAddWmsStockInDetail() {
      this.materialDialogVisible = true;
    },
    /** 入库单明细删除按钮操作 */
    handleDeleteWmsStockInDetail() {
      if (this.checkedWmsStockInDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单明细数据");
      } else {
        const wmsStockInDetailList =
          this.stockInDetailform.wmsStockInDetailList;
        const checkedWmsStockInDetail = this.checkedWmsStockInDetail;

        // 1. 收集被删除项的 ID（根据选中的 index）
        const deletedIds = checkedWmsStockInDetail
          .map((index) => wmsStockInDetailList[index - 1]?.id) // 根据 index 获取 id index的要从0开始，所以这里要-1！！
          .filter((id) => id !== undefined); // 过滤无效值
        console.log("被删除的 ID:", deletedIds);

        this.stockInDetailform.wmsStockInDetailList =
          wmsStockInDetailList.filter((item) => {
            return checkedWmsStockInDetail.indexOf(item.index) == -1;
          });
        // 这里是Set
        deletedIds.forEach((id) => this.existingIds.delete(id));
        // 3. 从 existingIds 中移除被删除项的 ID
        // if (this.existingIds instanceof Set) {
        //   // 如果是 Set，直接删除对应 ID
        //   deletedIds.forEach((id) => this.existingIds.delete(id));
        // } else {
        //   // 如果是数组，过滤掉已删除的 ID
        //   this.existingIds = this.existingIds.filter(
        //     (id) => !deletedIds.includes(id)
        //   );
        // }
        console.log("更新后的 existingIds:", this.existingIds);
      }
    },
    /** 复选框选中数据 */
    handleWmsStockInDetailSelectionChange(selection) {
      this.checkedWmsStockInDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/production_receipt/export",
        {
          ...this.queryParams,
        },
        `生产入库_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  padding: 8px 0 !important;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

// .borderLeftStyle {
//   color: #72767b;
//   font-size: 16px;
//   margin-left: 5px;
//   border-left: 3px solid #1583e9;
//   margin-bottom: 15px;
//   font-weight: 300;
//   padding-left: 6px;
//   line-height: 22px;
// }</style>