import request from '@/utils/request'

// 查询IQC检验配置列表
export function listConfig(query) {
  return request({
    url: '/quality/config/list',
    method: 'get',
    params: query
  })
}

// 查询IQC检验配置详细
export function getConfig(id) {
  return request({
    url: '/quality/config/' + id,
    method: 'get'
  })
}

// 新增IQC检验配置
export function addConfig(data) {
  return request({
    url: '/quality/config',
    method: 'post',
    data: data
  })
}

// 修改IQC检验配置
export function updateConfig(data) {
  return request({
    url: '/quality/config',
    method: 'put',
    data: data
  })
}

// 删除IQC检验配置
export function delConfig(id) {
  return request({
    url: '/quality/config/' + id,
    method: 'delete'
  })
}
