import request from '@/utils/request'

// 查询物料移库列表
export function listMove(query) {
  return request({
    url: '/system/move/list',
    method: 'get',
    params: query
  })
}

// 查询物料移库详细
export function getMove(id) {
  return request({
    url: '/system/move/' + id,
    method: 'get'
  })
}

// 新增物料移库
export function addMove(data) {
  return request({
    url: '/system/move',
    method: 'post',
    data: data
  })
}

// 修改物料移库
export function updateMove(data) {
  return request({
    url: '/system/move',
    method: 'put',
    data: data
  })
}

// 删除物料移库
export function delMove(id) {
  return request({
    url: '/system/move/' + id,
    method: 'delete'
  })
}
  //删除物料移库明细表
  export function delMoveDetail(id) {
    return request({
      url: '/system/moveDetail/' + id,
      method: 'delete'
    })
}
