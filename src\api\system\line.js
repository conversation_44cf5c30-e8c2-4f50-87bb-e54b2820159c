import request from '@/utils/request'

// 查询产线列表
export function listLine(query) {
  return request({
    url: '/system/line/list',
    method: 'get',
    params: query
  })
}

// 查询产线详细
export function getLine(id) {
  return request({
    url: '/system/line/' + id,
    method: 'get'
  })
}

// 新增产线
export function addLine(data) {
  return request({
    url: '/system/line',
    method: 'post',
    data: data
  })
}

// 修改产线
export function updateLine(data) {
  return request({
    url: '/system/line',
    method: 'put',
    data: data
  })
}

// 删除产线
export function delLine(id) {
  return request({
    url: '/system/line/' + id,
    method: 'delete'
  })
}
