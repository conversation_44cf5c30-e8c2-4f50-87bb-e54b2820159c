import request from '@/utils/request'

// 查询移库单明细列表
export function listMoveDetail(query) {
  return request({
    url: '/system/moveDetail/list',
    method: 'get',
    params: query
  })
}

// 查询移库单明细详细
export function getMoveDetail(id) {
  return request({
    url: '/system/moveDetail/' + id,
    method: 'get'
  })
}

// 新增移库单明细
export function addMoveDetail(data) {
  return request({
    url: '/system/moveDetail',
    method: 'post',
    data: data
  })
}

// 修改移库单明细
export function updateMoveDetail(data) {
  return request({
    url: '/system/moveDetail',
    method: 'put',
    data: data
  })
}

// 删除移库单明细
export function delMoveDetail(id) {
  return request({
    url: '/system/moveDetail/' + id,
    method: 'delete'
  })
}
