import request from '@/utils/request'

// 查询箱信息列表
export function listBox(query) {
  return request({
    url: '/system/box/list',
    method: 'get',
    params: query
  })
}

export function listBoxBd(query) {
  return request({
    url: '/system/box/Bd',
    method: 'get',
    params: query
  })
}

// 查询箱信息详细
export function getBox(id) {
  return request({
    url: '/system/box/' + id,
    method: 'get'
  })
}

// 新增箱信息
export function addBox(data) {
  return request({
    url: '/system/box',
    method: 'post',
    data: data
  })
}

// 修改箱信息
export function updateBox(data) {
  return request({
    url: '/system/box',
    method: 'put',
    data: data
  })
}

// 删除箱信息
export function delBox(id) {
  return request({
    url: '/system/box/' + id,
    method: 'delete'
  })
}
