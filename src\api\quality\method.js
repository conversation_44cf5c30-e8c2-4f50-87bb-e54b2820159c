import request from '@/utils/request'

// 查询抽样方法列表
export function listMethod(query) {
  return request({
    url: '/quality/method/list',
    method: 'get',
    params: query
  })
}

// 查询抽样方法详细
export function getMethod(id) {
  return request({
    url: '/quality/method/' + id,
    method: 'get'
  })
}

// 新增抽样方法
export function addMethod(data) {
  return request({
    url: '/quality/method',
    method: 'post',
    data: data
  })
}

// 修改抽样方法
export function updateMethod(data) {
  return request({
    url: '/quality/method',
    method: 'put',
    data: data
  })
}

// 删除抽样方法
export function delMethod(id) {
  return request({
    url: '/quality/method/' + id,
    method: 'delete'
  })
}
