import request from '@/utils/request'

// 查询期出库存列表
export function listwmsBoxPeriodOut(query) {
  return request({
    url: '/system/wmsBoxPeriodOut/list',
    method: 'get',
    params: query
  })
}

// 查询期出库存详细
export function getwmsBoxPeriodOut(id) {
  return request({
    url: '/system/wmsBoxPeriodOut/' + id,
    method: 'get'
  })
}

// 新增期出库存
export function addwmsBoxPeriodOut(data) {
  return request({
    url: '/system/wmsBoxPeriodOut',
    method: 'post',
    data: data
  })
}

// 修改期出库存
export function updatewmsBoxPeriodOut(data) {
  return request({
    url: '/system/wmsBoxPeriodOut',
    method: 'put',
    data: data
  })
}


// 删除期出库存
export function delwmsBoxPeriodOut(id) {
  return request({
    url: '/system/wmsBoxPeriodOut/' + id,
    method: 'delete'
  })
}
