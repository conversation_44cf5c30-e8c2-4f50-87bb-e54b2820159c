<template>
  <div>
    <div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="库区编码" prop="areaCode">
          <el-input v-model="queryParams.areaCode" placeholder="请输入库区编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="库区名称" prop="areaName">
          <el-input v-model="queryParams.areaName" placeholder="请输入库区名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:area:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:area:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:area:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:area:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="areaList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="库区编码" align="center" prop="areaCode" />
        <el-table-column label="库区名称" align="center" prop="areaName" />
        <el-table-column label="显示顺序" align="center" prop="orderNum" />
        <el-table-column label="库区类型" align="center" prop="areaType" />
        <!-- <el-table-column label="状态" align="center" prop="status" /> -->
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:area:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:area:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改库区对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item label="库区编码" prop="areaCode" style="width: 240px">
                <el-input v-model="form.areaCode" placeholder="请输入库区编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="库区名称" prop="areaName" style="width: 240px">
                <el-input v-model="form.areaName" placeholder="请输入库区名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="库区类型" prop="areaType" style="width: 240px">
                <el-select v-model="form.areaType" placeholder="请选择库区类型" style="width: 240px" filterable clearable>
                  <el-option v-for="item in storageTyList" :key="item.id"
                    :label="item.storageTypeCode + '-' + item.storageTypeName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12"></el-col>
          </el-row>
          <el-form-item label="描述" prop="remark" style="width: 700px">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入描述" />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { listStorageType } from "@/api/system/storageType";

import {
  listArea,
  getArea,
  delArea,
  addArea,
  updateArea,
} from "@/api/system/area";
import { listWarehouse } from "@/api/system/warehouse";
export default {
  props: {
    parentData: {
      type: Object,
      required: true,  // 根据实际情况设为 true 或 false
      default: () => ({})  // 默认值为空对象
    }
  },
  name: "Area",
  data() {
    return {
      //库区类型列表
      storageTyList: [],
      //仓库列表
      warehouseList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库区表格数据
      areaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        areaCode: null,
        areaName: null,
        warehouseId: null,
        orderNum: null,
        status: null,
        comId: null,
        areaType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        areaCode: [
          { required: true, message: "库区编码不能为空", trigger: "blur" },
        ],
        areaName: [
          { required: true, message: "库区名称不能为空", trigger: "blur" },
        ],
        warehouseId: [
          { required: true, message: "请选择仓库", trigger: "blur" },
        ],
        areaType: [
          { required: true, message: "库区类型不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.ckList();
    this.storageTypeList();
  },
  methods: {
    /** 查询库区类型列表 */
    storageTypeList() {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        storageTypeCode: null,
        storageTypeName: null,
        updateBy: null,
        updateTime: null,
        comId: null,
      };
      listStorageType(queryParams).then((response) => {
        console.log("库区类型列表", response);

        this.storageTyList = response.rows;
      });
    },

    /** 查询仓库列表 */
    ckList() {
      let qur = {
        pageNum: 1,
        pageSize: 1000,
        warehouseCode: null,
        warehouseName: null,
        orderNum: null,
        leader: null,
        phone: null,
        status: null,
        comId: null,
      };
      listWarehouse(qur).then((response) => {
        // console.log("仓库列表",response);

        this.warehouseList = response.rows;
      });
    },
    /** 查询库区列表 */
    getList() {
      this.loading = true;
      this.queryParams.warehouseId = this.parentData.id;
      listArea(this.queryParams).then((response) => {
        this.areaList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$emit("refresh");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        areaCode: null,
        areaName: null,
        warehouseId: null,
        orderNum: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        areaType: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.warehouseId = this.parentData.id;
      this.open = true;
      this.title = "添加库区";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getArea(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库区";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateArea(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addArea(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }

        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除库区编号为"' + ids + '"的数据项？')
        .then(function () {
          return delArea(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/area/export",
        {
          ...this.queryParams,
        },
        `area_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
