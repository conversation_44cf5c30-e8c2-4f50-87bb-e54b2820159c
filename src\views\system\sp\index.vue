<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="100px"
      >
        <el-form-item label="方案编码" prop="ipqcSpCode">
          <el-input
            v-model="queryParams.ipqcSpCode"
            placeholder="请输入方案编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="方案名称" prop="ipqcSpName">
          <el-input
            v-model="queryParams.ipqcSpName"
            placeholder="请输入方案名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="频次安排" prop="planFrequency">
          <el-input
            v-model="queryParams.planFrequency"
            placeholder="请输入频次安排"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="间隔时间(天)" prop="intervalDay">
          <el-input
            v-model="queryParams.intervalDay"
            placeholder="请输入间隔时间(天)"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="计划开始时间" prop="planStartDate">
          <el-date-picker
            clearable
            v-model="queryParams.planStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="planEndDate">
          <el-date-picker
            clearable
            v-model="queryParams.planEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="组织" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入组织"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:sp:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:sp:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:sp:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:sp:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="spList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="方案编码" align="center" prop="ipqcSpCode" />
        <el-table-column label="方案名称" align="center" prop="ipqcSpName" />
        <el-table-column label="频次安排" align="center" prop="planFrequency" />
        <el-table-column
          label="间隔时间(天)"
          align="center"
          prop="intervalDay"
        />
        <el-table-column
          label="计划开始时间"
          align="center"
          prop="planStartDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planStartDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="计划结束时间"
          align="center"
          prop="planEndDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planEndDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="备注" align="center" prop="remark" />

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:sp:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:sp:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改设备巡检方案对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >

        <el-form ref="form" :model="form" :rules="rules">
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
          <el-form-item label="方案编码" prop="ipqcSpCode">
            <el-input v-model="form.ipqcSpCode" placeholder="请输入方案编码" style="width: 200px" />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="方案名称" prop="ipqcSpName">
            <el-input v-model="form.ipqcSpName" placeholder="请输入方案名称" style="width: 200px"  />
          </el-form-item>
          </el-col>
        </el-row>

           <el-row :gutter="10" class="mb8">
            <el-col :span="12">
          <el-form-item label="频次安排" prop="planFrequency"  >
            <el-input
              v-model="form.planFrequency"
              placeholder="请输入频次安排"
              style="width: 200px"
            />
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="间隔时间(天)" prop="intervalDay"  >
            <el-input
              v-model="form.intervalDay"
              placeholder="请输入间隔时间(天)"
              style="width: 200px"
            />
          </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10" class="mb8">
            <el-col :span="12">
          <el-form-item label="计划开始时间" prop="planStartDate" >
            <el-date-picker
              clearable
              v-model="form.planStartDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择计划开始时间"
              style="width: 200px"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
            <el-col :span="12">
          <el-form-item label="计划结束时间" prop="planEndDate">
            <el-date-picker
              clearable
              v-model="form.planEndDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择计划结束时间"
              style="width: 200px"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
        </el-row>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>



        </el-form>

        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>
``
<script>
import { listSp, getSp, delSp, addSp, updateSp } from "@/api/system/sp";

export default {
  name: "Sp",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备巡检方案表格数据
      spList: [],
      // 方案编码数组
      codes: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ipqcSpCode: null,
        ipqcSpName: null,
        planFrequency: null,
        intervalDay: null,
        planStartDate: null,
        planEndDate: null,
        status: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备巡检方案列表 */
    getList() {
      this.loading = true;
      listSp(this.queryParams).then((response) => {
        this.spList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ipqcSpCode: null,
        ipqcSpName: null,
        planFrequency: null,
        intervalDay: null,
        planStartDate: null,
        planEndDate: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.codes = selection.map((item) => item.ipqcSpCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备巡检方案";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSp(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备巡检方案";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSp(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSp(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.ipqcSpCode || this.codes;
      this.$modal
        .confirm('是否确认删除设备巡检方案编号为"' + codes + '"的数据项？')
        .then(function () {
          return delSp(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/sp/export",
        {
          ...this.queryParams,
        },
        `sp_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.ul-height {
  height: 62vh;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  margin-right: 3%;
  overflow-y: scroll;
  overflow-x: hidden;
  .colClass {
    height: 48%;
    margin-bottom: 20px;
  }
  ::v-deep .el-card {
    height: 450px;
  }
  .imgClass {
    width: 500px;
    // height: 100%;
    display: block;
    height: 200px;
    background: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    ::v-deep .el-image__inner {
      height: 100%;
      width: 60%;
    }
  }
  .contentTitle {
    font-weight: 500;
    font-size: 18px;
    margin-left: 0;
    margin-bottom: 3px;
  }
  .contentSpan {
    font-size: 15px;
    margin-left: 0;
  }
  li {
    border: 1px solid #dcdfe6;
    width: 15%;
    height: 410px;
    color: #1c1b1b;
    float: left;
    list-style: none;
    margin: 5px;
    cursor: pointer;
    overflow: hidden;
  }
}
.emptyClass {
  background-color: #fff;
  border-radius: 20px;
  width: 300px;
  height: 350px;
  margin: auto;
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
}
</style>