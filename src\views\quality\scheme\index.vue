<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-row :gutter="10" class="mb8">
        <el-col :span="8">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:scheme:add']"
            >新建方案</el-button
          >
        </el-col>
    
      </el-row>

      <!-- 搜索 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="5">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <!-- <el-form-item>
              <el-input
                v-model="queryParams.schemeCode"
                placeholder="请输入方案名称或编码"
                clearable
                @keyup.enter.native="handleQuery"
              /> -->
               <el-form-item>
              <el-input
                v-model="queryParams.schemeCode"
                placeholder="请输入抽样方案名称或编码"
                clearable
                @input="handleInputSearch"
              />
            </el-form-item>
            <!-- <el-form-item>
              <el-button
                type="primary"
                plain
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                v-hasPermi="['quality:scheme:list']"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item> -->
          </el-form>

          <!-- 左侧主表数据 -->
          <el-tree
            :data="schemeList"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            ref="tree"
            :highlight-current="true"
            class="custom-tree"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <!-- <span>{{ data.schemeCode }}[{{ data.schemeName}}][{{ data.sampleAql }}]</span> -->
              <span>{{ node.label }}</span>
              <span>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => append(data)"
                  v-hasPermi="['quality:scheme:edit']"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => remove(node, data)"
                  v-hasPermi="['quality:scheme:remove']"
                >
                  删除
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-col>

        <!-- 右侧展示案数据 子表method-->
        <el-col :span="19" v-if="methodDisable">
          <el-row :gutter="10" class="mb8">
            <el-col :span="16">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddMethod"
                v-hasPermi="['quality:method:add']"
                >新建样本</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>
          <el-table
            height="62vh"
            v-loading="loading"
            :data="methodList"
            @selection-change="handleSelectionChange"
          >
           <el-table-column type="index" width="55" align="center" />
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="样本数量" align="center" prop="sampleNum" />
            <el-table-column label="字母表" align="center" prop="capital" />
            <el-table-column label="拒收数" align="center" prop="rejectNum" />
            <el-table-column label="允收数" align="center" prop="acceptNum" />
            <el-table-column label="起始批量" align="center" prop="startNum" />
            <el-table-column label="截止批量" align="center" prop="endNum" />
            <el-table-column label="数量" align="center" prop="number" />
            <!-- <el-table-column
              label="抽样主表id"
              align="center"
              prop="sampleId"
            /> -->
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="组织" align="center" prop="comId" />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateMethod(scope.row)"
                  v-hasPermi="['quality:method:edit']"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-deletehandleDeleteMethod"
                  @click="handleDeleteMethod(scope.row)"
                  v-hasPermi="['quality:method:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改抽样方法对话框 子抽屉-->
      <el-drawer
        :title="title"
        :visible.sync="openMethod"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="formMethod" :model="formMethod" :rules="methodrules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="抽样方法子表信息" name="1">
          <el-form-item label="样本数量" prop="sampleNum" style="width: 240px">
            <el-input
              v-model="formMethod.sampleNum"
              placeholder="请输入样本数量"
            />
          </el-form-item>
          <el-form-item label="字母表" prop="capital" style="width: 240px">
            <el-input v-model="formMethod.capital" placeholder="请输入字母表" />
          </el-form-item>
          <el-form-item label="拒收数" prop="rejectNum" style="width: 240px">
            <el-input
              v-model="formMethod.rejectNum"
              placeholder="请输入拒收数"
            />
          </el-form-item>
          <el-form-item label="允收数" prop="acceptNum" style="width: 240px">
            <el-input
              v-model="formMethod.acceptNum"
              placeholder="请输入允收数"
            />
          </el-form-item>
          <el-form-item label="起始批量" prop="startNum" style="width: 240px">
            <el-input
              v-model="formMethod.startNum"
              placeholder="请输入起始批量"
            />
          </el-form-item>
          <el-form-item label="截止批量" prop="endNum" style="width: 240px">
            <el-input
              v-model="formMethod.endNum"
              placeholder="请输入截止批量"
            />
          </el-form-item>
          <el-form-item label="数量" prop="number" style="width: 240px">
            <el-input v-model="formMethod.number" placeholder="请输入数量" />
          </el-form-item>
          <!-- <el-form-item label="抽样主表id" prop="sampleId" style="width: 240px">
            <el-input
              v-model="formMethod.sampleId"
              placeholder="请输入抽样主表id"
            />
          </el-form-item> -->
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="formMethod.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <!-- <el-form-item label="组织" prop="comId" style="width: 240px">
            <el-input v-model="formMethod.comId" placeholder="请输入组织" />
          </el-form-item> -->
          </el-collapse-item>
</el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitFormMethod">确 定</el-button>
          <el-button @click="cancelMethod">取 消</el-button>
        </div>
      </el-drawer>

      <!-- 添加或修改抽样方案对话框 (主drawer) -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
            <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="抽样方法信息" name="1">

          <el-form-item label="方案编码" prop="schemeCode" style="width: 240px">
            <el-input v-model="form.schemeCode" placeholder="请输入方案编码" />
          </el-form-item>
          <el-form-item label="方案名称" prop="schemeName" style="width: 240px">
            <el-input v-model="form.schemeName" placeholder="请输入方案名称" />
          </el-form-item>
          <el-form-item label="严格度" prop="strictLevel" style="width: 240px">
            <el-select v-model="form.strictLevel">
              <el-option
                v-for="item in dict.type.iqc_insp_config_strictness"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <!-- <el-input v-model="form.strictLevel" placeholder="请输入严格度" /> -->
          </el-form-item>
          <!-- ------------------------------复选框-------------------------------------------------- -->
          <el-form-item label="方案类型" prop="schemeType">
            <el-radio-group v-model="form.schemeType">
              <el-radio
                v-for="dict in dict.type.scheme_type"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <!-- AQL 类型参数 -->
          <div v-if="form.schemeType === 'AQL'">
            <el-form-item label="检验水平" prop="insepectionLevel">
              <el-select v-model="form.insepectionLevel">
                <el-option
                  v-for="item in dict.type.insepection_level"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="AQL" prop="sampleAql">
              <el-select v-model="form.sampleAql">
                <el-option
                  v-for="item in dict.type.aql"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <!-- <el-input v-model="form.sampleAql" placeholder="请输入AQL值" /> -->
            </el-form-item>
          </div>

          <!-- 比例抽检类型参数 -->
          <div v-if="form.schemeType === 'scale'">
            <el-form-item label="比例" prop="schemePercent">
              <el-input
                v-model.number="form.schemePercent"
                :min="0"
                :max="100"
                placeholder="请输入百分比"
                @input="handlePercentageInput"
              >
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </div>

          <!-- 固定值类型参数 -->
          <div v-if="form.schemeType === 'fixed'">
            <el-form-item label="数量" prop="schemeNumber">
              <el-input
                v-model.number="form.schemeNumber"
                type="number"
                :min="1"
                placeholder="请输入固定数量"
                @input="handleFixedValueInput"
              />
            </el-form-item>
          </div>
          <!-- --------------------------------------------------------------------------------- -->
          <!-- <el-form-item label="检验水平" prop="insepectionLevel" style="width: 240px;">
          <el-input v-model="form.insepectionLevel" placeholder="请输入检验水平" />
        </el-form-item>
        <el-form-item label="AQL" prop="sampleAql" style="width: 240px;">
          <el-input v-model="form.sampleAql" placeholder="请输入AQL" />
        </el-form-item>
        <el-form-item label="数量，按固定值时显示字段" prop="schemeNumber" style="width: 240px;">
          <el-input v-model="form.schemeNumber" placeholder="请输入数量，按固定值时显示字段" />
        </el-form-item>
        <el-form-item label="比例，按比例时显示字段" prop="schemePercent" style="width: 240px;">
          <el-input v-model="form.schemePercent" placeholder="请输入比例，按比例时显示字段" />
        </el-form-item> -->

          <el-form-item label="备注" prop="remark" style="width: 240px">
            <el-input v-model="form.remark" type="textarea" />
          </el-form-item>
          </el-collapse-item>
</el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listScheme,
  getScheme,
  delScheme,
  addScheme,
  updateScheme,
  listSchemeMenu,
} from "@/api/quality/scheme";
import {
  listMethod,
  getMethod,
  delMethod,
  addMethod,
  updateMethod,
} from "@/api/quality/method";
// 引入防抖函数
import { debounce } from 'lodash-es';
export default {
  name: "Scheme",
  dicts: [
    "iqc_insp_config_strictness",
    "aql",
    "insepection_level",
    "scheme_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 抽样方案表格数据
      schemeList: [],
      activeNames: ["1", "2"],
      activeNamesInfo: ["1", "2"],
      // 抽样方法表格数据
      methodList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openMethod: false,
      methodDisable: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sampleAql: null,
        strictLevel: null,
        insepectionLevel: null,
        schemeCode: null,
        schemeName: null,
        schemeType: null,
        schemeNumber: null,
        schemePercent: null,
        sampleUnit: null,
        comId: null,
        remark: null,
        sampleNum: null,
        capital: null,
        rejectNum: null,
        acceptNum: null,
        startNum: null,
        endNum: null,
        number: null,
        sampleId: null,
      },
      // 表单参数
      form: {
        schemeType: "AQL", // 默认选中类型
        inspectionLevel: "",
        aqlValue: "",
        percentage: "",
        quantity: "",
      },
      formMethod: {},
      // 表单校验
      rules: {
        schemeCode: [
          { required: true, message: "方案编码不能为空", trigger: "blur" },
        ],
        schemeName: [
          { required: true, message: "方案名称不能为空", trigger: "blur" },
        ],
        strictLevel: [
          { required: true, message: "方案严格度不能为空", trigger: "blur" },
        ],
        schemeType: [
          { required: true, message: "方案类型能为空", trigger: "blur" },
        ],
        insepectionLevel: [
          { required: true, message: "检验水平不能为空", trigger: "blur" },
        ],
        sampleAql: [
          { required: true, message: "方案AQL不能为空", trigger: "blur" },
        ],
        schemePercent: [
          { required: true, message: "比例不能为空", trigger: "blur" },
          { type: "number", message: "必须为数字", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value < 0 || value > 100) {
                callback(new Error("比例必须在0-100之间"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        schemeNumber: [
          { required: true, message: "数量不能为空", trigger: "blur" },
          { type: "number", message: "必须为数字", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error("数量必须大于0"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      methodrules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
        standardValue: [], // 延迟设置校验规则
        upperLimit: [], // 延迟设置校验规则
        lowerLimit: [], // 延迟设置校验规则
        sampleNum: [
        { validator: this.validateNumber, trigger: "blur" }  // 添加自定义校验规则
      ],
        rejectNum: [
          { validator: this.validateNumber, trigger: "blur" },
        ],
        acceptNum: [
          { validator: this.validateNumber, trigger: "blur" },
        ],
        startNum: [
          { validator: this.validateNumber, trigger: "blur" },
        ],
        endNum: [
          { validator: this.validateNumber, trigger: "blur" },
        ],
        number: [
          { validator: this.validateNumber, trigger: "blur" },
        ],
      },
    };
  },

  // mounted() {},
  created() {
    this.getList();
    // 在生命周期中动态设置校验规则
    this.methodrules.standardValue.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.methodrules.upperLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.methodrules.lowerLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.methodrules.sampleNum.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
  },
  methods: {
 // 带防抖的输入处理
   handleInputSearch: debounce(function() {
    // 统一在此处处理查询，移除watch中的重复逻辑
    this.handleQuery();
  }, 300),

  handleQuery() {
    this.queryParams.pageNum = 1;
    this.getList(); // 仅触发左侧数据更新
  },

    getListMethod() {
      this.loading = true;
      listMethod(this.queryParams).then((response) => {
        // this.methodList = this.sortArrayByField(response.rows, 'createTime');
        this.methodList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 处理百分比输入
    handlePercentageInput(value) {
      if (value === null || value === "") return;
      // 限制输入为数字且范围在0-100
      let num = Math.min(100, Math.max(0, Number(value)));
      this.form.schemePercent = num;
    },
    // 处理固定值输入
    handleFixedValueInput(value) {
      if (value === null || value === "") return;
      // 限制输入为正整数
      let num = Math.max(1, Math.floor(Number(value)));
      this.form.schemeNumber = num;
    },

    getNodeLabel(nodeKey) {
      const findNode = (nodes, key) => {
        for (const node of nodes) {
          if (node.id === key) {
            return node.label; // 返回匹配节点的 label
          }
          if (node.children) {
            const result = findNode(node.children, key);
            if (result) return result;
          }
        }
        return null;
      };
      return findNode(this.schemeList, nodeKey) || "未知类型"; // 如果找不到，返回“未知类型”
    },
    append(data) {
      this.reset();
      const id = data.id || this.ids;
      getScheme(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改抽样方案";
      });
    },
    // 处理树节点点击事件
    handleNodeClick(node) {
      console.log(node, "id");
      if (node.schemeType == "AQL") {
        this.methodDisable = true;
      } else {
        this.methodDisable = false;
      }
      this.queryParams.sampleId = node.id; // 将节点的 id 作为所属类型
      this.formMethod.sampleId = node.id; // 同步赋值节点的 id
      this.formMethod.schemeType = node.schemeType;
      this.getListMethod(); // 调用获取表格数据的方法
    },
    remove(node, data) {
      const ids = data.id || this.ids;
      const codes = data.schemeCode || this.codes;
      this.$modal
        .confirm('是否确认删除抽样方案编码为"' + codes + '"的数据项？')
        .then(function () {
          return delScheme(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 查询抽样方案列表 */
    async getList() {
       listSchemeMenu(this.queryParams).then((response) => {
        this.schemeList = response.data;

        // 确保树形数据已加载
        this.$nextTick(() => {
          if (this.schemeList.length > 0) {
            if (this.formMethod.sampleId != null) {
              this.$refs.tree.setCurrentKey(this.formMethod.sampleId);
            } else {
              console.log(this.schemeList[0].id, "this.schemeList[0].id");
              this.queryParams.sampleId = this.schemeList[0].id;
              this.$refs.tree.setCurrentKey(this.schemeList[0].id); // 默认选中树形控件的第一个节点
              this.formMethod.sampleId = this.schemeList[0].id;
              this.formMethod.schemeType = this.schemeList[0].schemeType;
            }
            if (this.formMethod.schemeType == "AQL") {
              this.methodDisable = true;
            } else {
              this.methodDisable = false;
            }
            this.getListMethod(); // 调用获取表格数据的方法
          }
        });
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelMethod() {
      this.openMethod = false;
      this.resetMethod();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sampleAql: null,
        strictLevel: null,
        insepectionLevel: null,
        schemeCode: null,
        schemeName: null,
        schemeType: null,
        schemeNumber: null,
        schemePercent: null,
        sampleUnit: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    resetMethod() {
      this.formMethod = {
        id: null,
        sampleNum: null,
        capital: null,
        rejectNum: null,
        acceptNum: null,
        startNum: null,
        endNum: null,
        number: null,
        sampleId: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("formmMethod");
    },
    /** 搜索按钮操作 */
    // handleQuery() {
    //   this.queryParams.pageNum = 1;
    //   // this.getListMethod();
    //   this.getList();
    // },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.nums = selection.map((item) => item.sampleNum);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加抽样方案";
    },
    handleAddMethod() {
      this.resetMethod();
      this.formMethod.sampleId = this.queryParams.sampleId; // 自动赋值所属类型的 id
      this.openMethod = true;
      this.title = "添加抽样方法";
    },
    /** 修改按钮操作（左侧） */
    append(row) {
      this.reset();
      const id = row.id || this.ids;
      getScheme(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改抽样方案";
      });
    },
    /** 修改按钮操作（表格） */
    handleUpdateMethod(row) {
      this.resetMethod();
      const id = row.id || this.ids;
      getMethod(id).then((response) => {
        this.formMethod = response.data;
        this.openMethod = true;
        this.title = "修改抽样方法";
      });
    },
    /** 提交按钮 */
    submitForm() {
      let params = {
        schemeType: this.form.schemeType,
      };

      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateScheme(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScheme(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 自定义校验规则：检查是否为数字
    validateNumber(rule, value, callback) {
      if (value === null || value === undefined || value === "") {
        callback(); // 允许为空
      } else if (!/^\d+(\.\d+)?$/.test(value)) {
        callback(new Error("请输入有效数字")); // 非数字时提示错误
      } else {
        callback(); // 校验通过
      }
    },
    submitFormMethod() {
      this.$refs["formMethod"].validate((valid) => {
        if (valid) {
          if (this.formMethod.id != null) {
            updateMethod(this.formMethod).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.openMethod = false;
              this.getList();
            });
          } else {
            addMethod(this.formMethod).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.openMethod = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除抽样方案编号为"' + ids + '"的数据项？')
        .then(function () {
          return delScheme(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleDeleteMethod(row) {
      const ids = row.id || this.ids;
      const nums = row.sampleNum || this.nums;
      this.$modal
        .confirm('是否确认删除抽样方法样本数量为"' + nums + '"的数据项？')
        .then(function () {
          return delMethod(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/scheme/export",
        {
          ...this.queryParams,
        },
        `抽样方案_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    
  },
};
</script>