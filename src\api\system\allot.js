import request from '@/utils/request'

// 查询调拨管理列表
export function listAllot(query) {
  return request({
    url: '/system/allot/list',
    method: 'get',
    params: query
  })
}

// 查询调拨管理详细
export function getAllot(id) {
  return request({
    url: '/system/allot/' + id,
    method: 'get'
  })
}

// 新增调拨管理
export function addAllot(data) {
  return request({
    url: '/system/allot',
    method: 'post',
    data: data
  })
}

// 修改调拨管理
export function updateAllot(data) {
  return request({
    url: '/system/allot',
    method: 'put',
    data: data
  })
}

// 删除调拨管理
export function delAllot(id) {
  return request({
    url: '/system/allot/' + id,
    method: 'delete'
  })
}
