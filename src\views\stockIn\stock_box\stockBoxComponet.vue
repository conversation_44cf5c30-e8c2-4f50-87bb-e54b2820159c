<template>
  <el-table height="52vh" v-loading="loading" :data="stock_boxList" @selection-change="handleSelectionChange">
    <el-table-column label="箱号" align="center" prop="boxNo" />
    <el-table-column label="数量" align="center" prop="qty" />
    <el-table-column label="仓库名称" align="center" prop="warehouseName" />
    <el-table-column label="库位名称" align="center" prop="locationName" />
    <el-table-column label="库区名称" align="center" prop="areaName" />
    <el-table-column label="批次" align="center" prop="batchNo" />
  </el-table>
  <!-- 
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" /> -->
</template>

<script>
import {
  listStock_box,
  getStock_box,
  delStock_box,
  addStock_box,
  updateStock_box,
} from "@/api/system/stock_box";

export default {
  props: ["stock_detail_id"],
  name: "stockBoxComponet",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入库单箱信息表格数据
      stock_boxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: this.stock_detail_id,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        comId: null,
        dateCode: null,
        expirationDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stockInId: [
          { required: true, message: "入库主表id不能为空", trigger: "blur" },
        ],
        stockInNo: [
          { required: true, message: "入库单号不能为空", trigger: "blur" },
        ],
        stockInDetailId: [
          { required: true, message: "入库明细id不能为空", trigger: "blur" },
        ],
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        warehouseId: [
          { required: true, message: "仓库id不能为空", trigger: "blur" },
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // this.loading = true;
    this.getList();
  },
  methods: {
    /** 查询入库单箱信息列表 */
    getList() {
      if (this.stock_detail_id && this.stock_detail_id != "") {
        this.loading = true;
        listStock_box(this.queryParams).then((response) => {
          this.stock_boxList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        dateCode: null,
        expirationDate: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加入库单箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStock_box(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改入库单箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateStock_box(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStock_box(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除入库单箱信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStock_box(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stock_box/export",
        {
          ...this.queryParams,
        },
        `入库单箱信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
