import request from '@/utils/request'

// 查询入库单明细列表
export function listStock_detail(query) {
  return request({
    url: '/system/stock_detail/list',
    method: 'get',
    params: query
  })
}

// 查询入库单明细列表-入库类型
export function listStock_detailType(query) {
  return request({
    url: '/system/stock_detail/listInType',
    method: 'get',
    params: query
  })
}

// 查询入库单明细详细
export function getStock_detail(id) {
  return request({
    url: '/system/stock_detail/' + id,
    method: 'get'
  })
}

// 新增入库单明细
export function addStock_detail(data) {
  return request({
    url: '/system/stock_detail',
    method: 'post',
    data: data
  })
}

// 修改入库单明细
export function updateStock_detail(data) {
  return request({
    url: '/system/stock_detail',
    method: 'put',
    data: data
  })
}

// 删除入库单明细
export function delStock_detail(id) {
  return request({
    url: '/system/stock_detail/' + id,
    method: 'delete'
  })
}
