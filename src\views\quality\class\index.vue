<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-row :gutter="10" class="mb8">
        <el-col :span="8">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:class:add']"
            >新增类型</el-button
          >
        </el-col>
        <!-- <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar> -->
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
               <el-form-item>
              <el-input
                v-model="queryParams.code"
                placeholder="请输入检验类型名称或编码"
                clearable
                @input="handleInputSearch"
              />
            </el-form-item>
            
          </el-form>
          <el-tree
            :data="classList"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            ref="tree"
            :highlight-current="true"
            class="custom-tree"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <span>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => append(data)"
                  v-hasPermi="['quality:class:edit']"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => remove(node, data)"
                  v-hasPermi="['quality:class:remove']"
                >
                  删除
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-col>
        
        <el-col :span="19">
          <!-- <el-table
            height="62vh"
            v-loading="loading"
            :data="classList"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="编码" align="center" prop="code" />
            <el-table-column label="名称" align="center" prop="name" />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['quality:class:edit']"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['quality:class:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table> -->
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="项目名称" prop="itemName">
              <el-input
                v-model="queryParams.itemName"
                placeholder="请输入项目名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>

            <el-form-item label="项目编码" prop="itemCode">
              <el-input
                v-model="queryParams.itemCode"
                placeholder="请输入项目编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddItem"
                v-hasPermi="['quality:item:add']"
                >新增</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdateItem"
                v-hasPermi="['quality:item:edit']"
                >修改</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDeleteItem"
                v-hasPermi="['quality:item:remove']"
                >删除</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExportItem"
                v-hasPermi="['quality:item:export']"
                >导出</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>

          <el-table
            height="62vh"
            v-loading="loading"
            :data="itemList"
            @selection-change="handleSelectionChange"
          >
          <el-table-column type="index" width="55" align="center" />
            <el-table-column type="selection" width="55" align="center" />
            <!-- <el-table-column label="" align="center" prop="id" /> -->
            <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
            <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
            <!-- <el-table-column label="接受水平" align="center" prop="acceptanceLevel" /> -->

            <el-table-column label="项目名称" align="center" prop="itemName" />
            <!-- <el-table-column label="附件" align="center" prop="attachment" /> -->
            <!-- <el-table-column label="下限值类型" align="center" prop="lowerType">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.compare_state"
                  :value="scope.row.lowerType"
                />
              </template>
            </el-table-column>
            <el-table-column label="上限值类型" align="center" prop="upperType">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.compare_state"
                  :value="scope.row.upperType"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="检验设备"
              align="center"
              prop="inspectionEq"
            />
            <el-table-column
              label="检验方法"
              align="center"
              prop="inspectionMethod"
            />
            <el-table-column label="单位" align="center" prop="itemUnit" /> -->
            <el-table-column label="所属工厂" align="center" prop="siteId" />
            <el-table-column label="项目编码" align="center" prop="itemCode" />
            <el-table-column
              label="所属类型"
              align="center"
              prop="inspectionClassType"
            >
              <template slot-scope="scope">
                <span>{{ getNodeLabel(scope.row.inspectionClassType) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="检验等级" align="center" prop="inspectionLevel" /> -->
            <el-table-column
              label="分析方法"
              align="center"
              prop="analysisMethod"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.analysis_method"
                  :value="scope.row.analysisMethod"
                />
              </template>
            </el-table-column>
            <!-- <el-table-column
              label="标准值"
              align="center"
              prop="standardValue"
            />
            <el-table-column label="上限值" align="center" prop="upperLimit" />
            <el-table-column label="下限值" align="center" prop="lowerLimit" />
            <el-table-column
              label="技术标准"
              align="center"
              prop="technicalStandard"
            /> -->
            <!-- <el-table-column label="结果类型" align="center" prop="resultType" />
      <el-table-column label="数据来源" align="center" prop="dataSource" />
      <el-table-column label="检验组数" align="center" prop="inspectionGroup" />
      <el-table-column label="误差范围下限" align="center" prop="errorRangeLowerLimit" />
      <el-table-column label="误差范围上限" align="center" prop="errorRangeUpperLimit" /> -->
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateItem(scope.row)"
                  v-hasPermi="['quality:item:edit']"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteItem(scope.row)"
                  v-hasPermi="['quality:item:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      /> -->

      <el-drawer
        :title="title"
        :visible.sync="openItem"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="formItem" :model="formItem" :rules="itemrules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="检验项目单信息" name="1">
          <!-- <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="接受水平" prop="acceptanceLevel" style="width: 240px;">
          <el-input v-model="form.acceptanceLevel" placeholder="请输入接受水平" />
        </el-form-item> -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="项目编码"
                prop="itemCode"
                style="width: 240px"
              >
                <el-input
                  v-model="formItem.itemCode"
                  placeholder="请输入项目编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="项目名称"
                prop="itemName"
                style="width: 240px"
              >
                <el-input
                  v-model="formItem.itemName"
                  placeholder="请输入项目名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分析方法"
                prop="analysisMethod"
                style="width: 240px"
              >
                <el-select
                  v-model="formItem.analysisMethod"
                  placeholder="请选择分析方法"
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.analysis_method"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-form-item label="附件" prop="attachment" style="width: 240px;">
          <el-input v-model="form.attachment" placeholder="请输入附件" />
        </el-form-item> -->
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="检验方法"
                prop="inspectionMethod"
                style="width: 240px"
              >
                <el-select
                  v-model="formItem.inspectionMethod"
                  placeholder="请选择检验方法"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="method in methodList"
                    :key="method.id"
                    :label="method.name"
                    :value="method.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="检验设备"
                prop="inspectionEq"
                style="width: 240px"
              >
                <el-select
                  v-model="formItem.inspectionEq"
                  placeholder="请选择检验设备"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="equipment in equipmentList"
                    :key="equipment.id"
                    :label="equipment.name"
                    :value="equipment.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="标准值"
                prop="standardValue"
                style="width: 240px"
              >
                <el-input
                  v-model="formItem.standardValue"
                  placeholder="请输入标准值"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="技术标准"
                prop="technicalStandard"
                style="width: 240px"
              >
                <el-input
                  v-model="formItem.technicalStandard"
                  placeholder="请输入技术标准"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="单位"
                prop="materialUnit"
                style="width: 240px"
              >
                <el-select
                  v-model="form.materialUnit"
                  placeholder="请选择单位"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in unitList"
                    :key="item.value"
                    :label="item.value"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属工厂" prop="siteId" style="width: 240px">
                <el-input
                  v-model="formItem.siteId"
                  placeholder="请输入所属工厂"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="formItem.analysisMethod == 'ration'">
            <el-col :span="6">
              <el-form-item
                label="上限值类型"
                prop="upperType"
                style="width: 200px"
              >
                <el-select
                  v-model="formItem.upperType"
                  placeholder="请选择上限值类型"
                  style="width: 200px"
                >
                  <el-option
                    v-for="dict in upperTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="上限值"
                prop="upperLimit"
                style="width: 200px"
              >
                <el-input
                  v-model="formItem.upperLimit"
                  placeholder="请输入上限值"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="下限值类型"
                prop="lowerType"
                style="width: 200px"
              >
                <el-select
                  v-model="formItem.lowerType"
                  placeholder="请选择下限值类型"
                  style="width: 200px"
                >
                  <el-option
                    v-for="dict in lowerTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="下限值"
                prop="lowerLimit"
                style="width: 200px"
              >
                <el-input
                  v-model="formItem.lowerLimit"
                  placeholder="请输入下限值"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="检验等级"
                prop="inspectionLevel"
                style="width: 240px"
              >
                <el-input
                  v-model="formItem.inspectionLevel"
                  placeholder="请输入检验等级"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- <el-form-item label="所属类型" prop="inspectionClassType" style="width: 240px;">
          <el-input
            v-model="form.inspectionClassType"
            disabled
            style="width: 240px;"
          />
        </el-form-item> -->

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="描述"
                prop="destructiveTestNum"
                style="width: 700px"
              >
                <el-input
                  type="textarea"
                  v-model="formItem.destructiveTestNum"
                  placeholder="请输入描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
</el-collapse-item>
</el-collapse>
          <!-- <el-form-item label="数据来源" prop="dataSource" style="width: 240px;">
          <el-input v-model="form.dataSource" placeholder="请输入数据来源" />
        </el-form-item>
        <el-form-item label="检验组数" prop="inspectionGroup" style="width: 240px;">
          <el-input v-model="form.inspectionGroup" placeholder="请输入检验组数" />
        </el-form-item>
        <el-form-item label="误差范围下限" prop="errorRangeLowerLimit" style="width: 240px;">
          <el-input v-model="form.errorRangeLowerLimit" placeholder="请输入误差范围下限" />
        </el-form-item>
        <el-form-item label="误差范围上限" prop="errorRangeUpperLimit" style="width: 240px;">
          <el-input v-model="form.errorRangeUpperLimit" placeholder="请输入误差范围上限" />
        </el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitFormItem">确 定</el-button>
          <el-button @click="cancelItem">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 添加或修改检验类型对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="编码" prop="code" style="width: 240px">
            <el-input v-model="form.code" placeholder="请输入编码" />
          </el-form-item>
          <el-form-item label="名称" prop="name" style="width: 240px">
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listClass,
  getClass,
  delClass,
  addClass,
  updateClass,
  listClassMenu,
} from "@/api/quality/base/class";
import { listUnit } from "@/api/system/unit";
import { listMenthod } from "@/api/quality/base/menthod";
import { listEquipment } from "@/api/quality/base/equipment";
import {
  listItem,
  getItem,
  delItem,
  addItem,
  updateItem,
} from "@/api/quality/item";
import { getDicts } from "@/api/system/dict/data";
// 引入防抖函数
import { debounce } from 'lodash-es';
export default {
  name: "Class",
  dicts: ["compare_state", "analysis_method"],
  data() {
    return {
      equipmentList: [], // 检验设备下拉框数据
      methodList: [], // 检验方法下拉框数据
      unitList: [], // 单位下拉框数据
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检验类型表格数据
      classList: [],
            activeNames: ["1", "2"],
      //明细
      itemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openItem: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        name: null,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      },
      // 表单参数
      form: {},
      formItem: {},
      //项目编码数组
      itemCodes: [],
      // 表单校验
      rules: {
        code: [{ required: true, message: "编码不能为空", trigger: "blur" }],
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
      },
      itemrules: {
        itemName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        siteId: [
          { required: true, message: "所属工厂不能为空", trigger: "blur" },
        ],
        itemCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" },
        ],
        inspectionClassType: [
          { required: true, message: "所属类型不能为空", trigger: "change" },
        ],
        inspectionLevel: [
          { required: true, message: "检验等级不能为空", trigger: "blur" },
        ],
        analysisMethod: [
          { required: true, message: "分析方法不能为空", trigger: "blur" },
        ],
        standardValue: [], // 延迟设置校验规则
        upperLimit: [], // 延迟设置校验规则
        lowerLimit: [], // 延迟设置校验规则
        upperTypeList: [],
        lowerTypeList: [],
      },
    };
  },
  created() {
    getDicts("compare_state").then((response) => {
      let upper = [];
      for (let i = 0; i < response.data.length; i++) {
        if (response.data[i].dictLabel != ">" && response.data[i].dictLabel != ">=") {
          upper.push(response.data[i]);
        }
      }
      this.upperTypeList = upper;
      let lower = [];
      for (let i = 0; i < response.data.length; i++) {
        console.log(response.data[i], 3633);
        if (response.data[i].dictLabel != "<" && response.data[i].dictLabel != "<=") {
          lower.push(response.data[i]);
        }
      }
      this.lowerTypeList = lower;
    });
    this.getList();
    this.loadEquipmentList(); // 加载检验设备数据
    this.loadUnitList(); // 加载单位数据
    this.loadMethodList(); // 加载检验方法数据
    // 在生命周期中动态设置校验规则
    this.itemrules.standardValue.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.itemrules.upperLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.itemrules.lowerLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
  },
  methods: {
    // 带防抖的输入处理
   handleInputSearch: debounce(function() {
    // 统一在此处处理查询，移除watch中的重复逻辑
    this.handleQuery();
  }, 300),

    getListItem() {
      this.loading = true;
      listItem(this.queryParams).then((response) => {
        // this.itemList = this.sortArrayByField(response.rows, 'createTime');
        this.itemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 加载检验设备数据
    loadEquipmentList() {
      listEquipment().then((response) => {
        this.equipmentList = response.rows; // 将接口返回的数据赋值给 equipmentlist
      });
    },
    // 加载检验方法数据
    loadMethodList() {
      listMenthod().then((response) => {
        this.methodList = response.rows; // 将接口返回的数据赋值给 methodList
      });
    },
    // 加载单位数据
    loadUnitList() {
      listUnit().then((response) => {
        this.unitList = response.rows; // 将接口返回的数据赋值给 unitList
      });
    },
    getNodeLabel(nodeKey) {
      const findNode = (nodes, key) => {
        for (const node of nodes) {
          if (node.id === key) {
            return node.label; // 返回匹配节点的 label
          }
          if (node.children) {
            const result = findNode(node.children, key);
            if (result) return result;
          }
        }
        return null;
      };
      return findNode(this.classList, nodeKey) || "未知类型"; // 如果找不到，返回“未知类型”
    },
    append(data) {
      this.reset();
      const id = data.id || this.ids;
      getClass(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检验类型";
      });
    },
    // 处理树节点点击事件
    handleNodeClick(node) {
      this.queryParams.inspectionClassType = node.id; // 将节点的 id 作为所属类型
      this.formItem.inspectionClassType = node.id; // 同步赋值节点的 id
      this.getListItem(); // 调用获取表格数据的方法
    },
    remove(node, data) {
      const ids = data.id || this.ids;
      const code = data.code || this.itemCodes;
      this.$modal
        .confirm('是否确认删除编号为"' + code + '"的数据项？')
        .then(function () {
          return delClass(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 查询检验类型列表 */
    async getList() {
      this.loading = true;
      listClassMenu(this.queryParams).then((response) => {
        this.classList = response.data;

        // 确保树形数据已加载
        this.$nextTick(() => {
          if (this.classList.length > 0) {
            if (this.formItem.inspectionClassType != null) {
              this.$refs.tree.setCurrentKey(this.formItem.inspectionClassType);
            } else {
              console.log(this.classList[0].id, "this.classList[0].id");
              this.queryParams.inspectionClassType = this.classList[0].id;
              this.$refs.tree.setCurrentKey(this.classList[0].id); // 默认选中树形控件的第一个节点
              this.formItem.inspectionClassType = this.classList[0].id;
            }
            this.getListItem(); // 调用获取表格数据的方法
          }
        });

        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.loadMethodList(); // 关闭弹窗时刷新检验方法列表
    },
    cancelItem() {
      this.openItem = false;
      this.resetItem();
      this.loadMethodList(); // 关闭弹窗时刷新检验方法列表
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    resetItem() {
      this.formItem = {
        id: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      };
      this.resetForm("formItem");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // this.getListItem();
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.itemCodes = selection.map((item) => item.itemCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检验类型";
    },

    handleAddItem() {
      this.resetItem();
      this.formItem.inspectionClassType = this.queryParams.inspectionClassType; // 自动赋值所属类型的 id
      this.openItem = true;
      this.title = "添加检验项目";
      this.loadMethodList(); // 弹窗时刷新检验方法列表
      this.loadEquipmentList(); // 弹窗时刷新检验设备列表
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getClass(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检验类型";
        this.loadMethodList(); // 弹窗时刷新检验方法列表
        this.loadEquipmentList(); // 弹窗时刷新检验设备列表
      });
    },
    handleUpdateItem(row) {
      this.resetItem();
      const id = row.id || this.ids;
      getItem(id).then((response) => {
        this.formItem = response.data;
        this.openItem = true;
        this.title = "修改检验项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateClass(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addClass(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 自定义校验规则：检查是否为数字
    validateNumber(rule, value, callback) {
      if (value === null || value === undefined || value === "") {
        callback(); // 允许为空
      } else if (!/^\d+(\.\d+)?$/.test(value)) {
        callback(new Error("请输入有效数字")); // 非数字时提示错误
      } else {
        callback(); // 校验通过
      }
    },
    submitFormItem() {
      this.$refs["formItem"].validate((valid) => {
        console.log("表单验证结果:", valid); // 添加调试日志
        if (valid) {
          if (this.formItem.id != null) {
            updateItem(this.formItem).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.openItem = false;
              this.getList();
            });
          } else {
            addItem(this.formItem).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.openItem = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除检验类型编号为"' + ids + '"的数据项？')
        .then(function () {
          return delClass(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    handleDeleteItem(row) {
      const ids = row.id || this.ids;
      const itemCodes = row.itemCode || this.itemCodes;
      this.$modal
        .confirm('是否确认删除检验项目编号为"' + itemCodes + '"的数据项？')
        .then(function () {
          return delItem(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    /** 导出按钮操作 */
    handleExportItem() {
      this.download(
        "quality/item/export",
        {
          ...this.queryParams,
        },
        `检验项目_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/class/export",
        {
          ...this.queryParams,
        },
        `检验类型_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss">
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
  padding-right: 8px;
  display: inline-block;
  height: 40px;
  line-height: 40px;
}
</style>