import request from '@/utils/request'

// 查询其他入库列表
export function listWms_other_stock_in(query) {
  return request({
    url: '/system/wms_other_stock_in/list',
    method: 'get',
    params: query
  })
}

// 查询其他入库详细
export function getWms_other_stock_in(id) {
  return request({
    url: '/system/wms_other_stock_in/' + id,
    method: 'get'
  })
}

// 新增其他入库
export function addWms_other_stock_in(data) {
  return request({
    url: '/system/wms_other_stock_in',
    method: 'post',
    data: data
  })
}

// 修改其他入库
export function updateWms_other_stock_in(data) {
  return request({
    url: '/system/wms_other_stock_in',
    method: 'put',
    data: data
  })
}

// 删除其他入库
export function delWms_other_stock_in(id) {
  return request({
    url: '/system/wms_other_stock_in/' + id,
    method: 'delete'
  })
}
