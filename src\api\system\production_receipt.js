import request from '@/utils/request'

// 查询生产入库列表
export function listProduction_receipt(query) {
  return request({
    url: '/system/production_receipt/list',
    method: 'get',
    params: query
  })
}

// 查询生产入库详细
export function getProduction_receipt(id) {
  return request({
    url: '/system/production_receipt/' + id,
    method: 'get'
  })
}

// 新增生产入库
export function addProduction_receipt(data) {
  return request({
    url: '/system/production_receipt',
    method: 'post',
    data: data
  })
}

// 修改生产入库
export function updateProduction_receipt(data) {
  return request({
    url: '/system/production_receipt',
    method: 'put',
    data: data
  })
}

// 删除生产入库
export function delProduction_receipt(id) {
  return request({
    url: '/system/production_receipt/' + id,
    method: 'delete'
  })
}
