import request from '@/utils/request'

// 查询客户管理列表
export function listCustom(query) {
  return request({
    url: '/system/custom/list',
    method: 'get',
    params: query
  })
}

// 查询客户管理详细
export function getCustom(id) {
  return request({
    url: '/system/custom/' + id,
    method: 'get'
  })
}

// 新增客户管理
export function addCustom(data) {
  return request({
    url: '/system/custom',
    method: 'post',
    data: data
  })
}

// 修改客户管理
export function updateCustom(data) {
  return request({
    url: '/system/custom',
    method: 'put',
    data: data
  })
}

// 删除客户管理
export function delCustom(id) {
  return request({
    url: '/system/custom/' + id,
    method: 'delete'
  })
}
