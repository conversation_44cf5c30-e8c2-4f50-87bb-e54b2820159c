<template>
  <div>
    <div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="库位编码" prop="locationCode">
          <el-input v-model="queryParams.locationCode" placeholder="请输入库位编码" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="库位名称" prop="locationName">
          <el-input v-model="queryParams.locationName" placeholder="请输入库位名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:warehouse_location:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:warehouse_location:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:warehouse_location:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:warehouse_location:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="warehouse_locationList"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="库位编码" align="center" prop="locationCode" />
        <el-table-column label="库位名称" align="center" prop="locationName" />
        <el-table-column label="显示顺序" align="center" prop="orderNum" />
        <!-- <el-table-column label="状态" align="center" prop="status" /> -->
        <el-table-column label="容量" align="center" prop="capacity" />
        <!-- <el-table-column label="排" align="center" prop="rowNum" />
        <el-table-column label="列" align="center" prop="colNum" />
        <el-table-column label="层" align="center" prop="layerNum" /> -->
        <el-table-column label="库位类型" align="center" prop="locationType" />
        <el-table-column label="备注" align="center" prop="remark" />
        <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:warehouse_location:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:warehouse_location:remove']">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-printer" @click="handlePrint(scope.row)"
              v-hasPermi="['system:warehouse_location:print']">打印</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改库位对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-row>
            <el-col :span="12">
              <el-form-item label="库位编码" prop="locationCode" style="width: 240px;">
                <el-input v-model="form.locationCode" placeholder="请输入库位编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="库位名称" prop="locationName" style="width: 240px;">
                <el-input v-model="form.locationName" placeholder="请输入库位名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="容量" prop="capacity" style="width: 240px;">
                <el-input v-model="form.capacity" placeholder="请输入容量" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排" prop="rowNum" style="width: 240px;">
                <el-input v-model="form.rowNum" placeholder="请输入排" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-form-item label="显示顺序" prop="orderNum">
            <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
          </el-form-item> -->

          <el-row>
            <el-col :span="12">
              <el-form-item label="列" prop="colNum" style="width: 240px;">
                <el-input v-model="form.colNum" placeholder="请输入列" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="层" prop="layerNum" style="width: 240px;">
                <el-input v-model="form.layerNum" placeholder="请输入层" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="库位类型" prop="locationType" style="width: 240px;">
                <el-select v-model="form.locationType" placeholder="请选择库位类型" clearable style="width: 240px" filterable>
                  <el-option v-for="dict in locationTypeList" :key="dict.id"
                    :label="dict.binTypeCode + '-' + dict.binTypeName" :value="dict.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark" style="width: 240px;">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listWarehouse_location,
  getWarehouse_location,
  delWarehouse_location,
  addWarehouse_location,
  updateWarehouse_location,
} from "@/api/system/warehouse_location";
import { listBinType } from "@/api/system/binType";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { listHiprint } from "@/api/system/hiprint";

export default {
  props: {
    parentData: {
      type: Object,
      required: true,  // 根据实际情况设为 true 或 false
      default: () => ({})  // 默认值为空对象
    }
  },
  name: "Warehouse_location",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库位表格数据
      warehouse_locationList: [],
      //库位类型list
      locationTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        locationCode: null,
        locationName: null,
        orderNum: null,
        status: null,
        areaId: null,
        comId: null,
        locationType: null,
        capacity: null,
        rowNum: null,
        colNum: null,
        layerNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        capacity: [
          { required: true, message: "容量不能为空", trigger: "blur" },
        ],
        rowNum: [{ required: true, message: "排不能为空", trigger: "blur" }],
        colNum: [{ required: true, message: "列不能为空", trigger: "blur" }],
        layerNum: [{ required: true, message: "层不能为空", trigger: "blur" }],

        locationCode: [
          { required: true, message: "库位编码不可为空", trigger: "blur" },
        ],
        locationName: [
          { required: true, message: "库位名称不可为空", trigger: "blur" },
        ],
        locationType: [
          { required: true, message: "库位类型不能为空", trigger: "blur" },
        ],
      },
      mypanel: {}, // 打印模板数据
    };
  },
  created() {
    this.getBinType()
    this.getList();
    this.getPrintList(); // 获取打印模板
  },
  methods: {
    /** 查询库位列表 */
    getList() {
      this.queryParams.areaId = this.parentData.id;
      this.loading = true;
      listWarehouse_location(this.queryParams).then((response) => {
        this.warehouse_locationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        locationCode: null,
        locationName: null,
        orderNum: null,
        status: null,
        areaId: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        locationType: null,
        capacity: null,
        rowNum: null,
        colNum: null,
        layerNum: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    getBinType() {
      let params = {
        pageNo: 1,
        pageSize: 10000,
      };
      listBinType(params).then((response) => {
        this.locationTypeList = response.rows;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.areaId = this.parentData.id;
      this.open = true;
      this.title = "添加库位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWarehouse_location(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库位";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWarehouse_location(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWarehouse_location(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除库位编号为"' + ids + '"的数据项？')
        .then(function () {
          ids
          return delWarehouse_location(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/warehouse_location/export",
        {
          ...this.queryParams,
        },
        `warehouse_location_${new Date().getTime()}.xlsx`
      );
    },
    /** 获取打印模板 */
    getPrintList() {
      listHiprint({ code: "warehouselocation" }).then((response) => {
        this.mypanel = JSON.parse(response.rows[0].printJson);
      });
    },
    /** 打印按钮操作 */
    handlePrint(row) {
      hiprint.init();
      var hiprintTemplate = new hiprint.PrintTemplate({
        template: this.mypanel,
        settingContainer: "#templateDesignDiv",
      });
      hiprintTemplate.print([row]);
    },
  },
};
</script>
