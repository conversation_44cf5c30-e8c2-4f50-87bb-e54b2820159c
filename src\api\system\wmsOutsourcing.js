import request from '@/utils/request'

// 查询委外发货列表
export function listWmsOutsourcing(query) {
  return request({
    url: '/system/wmsOutsourcing/list',
    method: 'get',
    params: query
  })
}

// 查询委外发货详细
export function getWmsOutsourcing(id) {
  return request({
    url: '/system/wmsOutsourcing/' + id,
    method: 'get'
  })
}

// 新增委外发货
export function addWmsOutsourcing(data) {
  return request({
    url: '/system/wmsOutsourcing',
    method: 'post',
    data: data
  })
}

// 修改委外发货
export function updateWmsOutsourcing(data) {
  return request({
    url: '/system/wmsOutsourcing',
    method: 'put',
    data: data
  })
}

// 删除委外发货
export function delWmsOutsourcing(id) {
  return request({
    url: '/system/wmsOutsourcing/' + id,
    method: 'delete'
  })
}
