/**
* 通用css样式布局处理
* Copyright (c) 2019 ruoyi
*/

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}

.pr5 {
  padding-right: 5px;
}

.pb5 {
  padding-bottom: 5px;
}

.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb8 {
  margin-bottom: 8px;
  margin-left: 3px ;
}

.ml5 {
  margin-left: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mr20 {
  margin-right: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.ml20 {
  margin-left: 20px;
}

.custom_title {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.drawer_submit{
  display: flex;
  justify-content: end;
  margin: 10px;
}

.custom-div {
  font-size: 17px;
  font-weight: 750
}

.el-collapse-item__content{
  margin-left: 8px;
}
/* 自定义树节点选中样式 */
.custom-tree .el-tree-node.is-current>.el-tree-node__content {
  background-color: #f0f7ff;
  /* 选中时的背景色，可以改成你想要的颜色 */
  color: #409eff;
  /* 选中时的文字颜色 */
}

/* 鼠标悬停样式 */
.custom-tree .el-tree-node__content:hover {
  background-color: #f5f5f5;
}

/* 增加 el-tree 的行高 */
.custom-tree .el-tree-node__content {
  height: 50px;
  /* 增加行高 */
  line-height: 50px;
  /* 确保文本垂直居中 */
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
  padding-right: 8px;
  display: inline-block;
  height: 40px;
  line-height: 40px;
}

/* 确保自定义节点内容正确显示 */
.custom-tree .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

.el-message-box__status+.el-message-box__message {
  word-break: break-word;
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog__wrapper.scrollbar .el-dialog .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 70vh;
  padding: 10px 20px 0;
}

.el-form {
  border-radius: 3px;
  padding: 10px;
  background: #FFF;
  margin-bottom: 10px;
}

.el-table {
  border-radius: 3px;
  padding: 0px;
  background: #FFF;
  margin-left: 6px;
}

.el-table {

  // border-radius: 3px;
  // padding:0px;
  // background:#FFF;
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 40px;
      font-size: 13px;
    }
  }

  .el-table__body-wrapper {
    .el-button [class*="el-icon-"]+span {
      margin-left: 1px;
    }
  }
}

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px
}

/** 表格布局 **/
.pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #FFFFFF none;
  border-radius: 4px;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

@media (max-width: 768px) {
  .pagination-container .el-pagination>.el-pagination__jump {
    display: none !important;
  }

  .pagination-container .el-pagination>.el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--mini {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link,
.el-table .el-dropdown-selfdefine {
  cursor: pointer;
  margin-left: 5px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content>.el-checkbox {
  margin-right: 8px;
}

.list-group-striped>.list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}

.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48D1CC;
  border-color: #48D1CC;
  color: #FFFFFF;
}

.el-button--cyan {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ed5565;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost {
  opacity: .8;
  color: #fff !important;
  background: #42b983 !important;
}

.top-right-btn {
  position: relative;
  float: right;
}



.el-collapse-item__header.is-active {
  border-bottom-color: transparent;
  font-size: 16px;
  // margin-left: 5px;
  border-left: 3px solid #1583e9;
  margin-bottom: 15px;
  font-weight: 500;
  padding-left: 16px;
  line-height: 18px;
  padding-top: 4px;
  padding-bottom: 4px;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  position: relative;
  /* 为伪元素定位做准备 */
  border-left: none;
  /* 移除原生的左边框 */
}

.el-collapse-item__header::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  // height: 60%;
  /* 控制蓝色条的高度（相对于父元素高度） */
  // width: 3px;
  width: 7px;
  height: 48px;
  margin-right: 7px;
  background-color: #0000ff;
}



.el-collapse-item__header {
  font-size: 16px;
  // margin-left: 5px;
  border-left: 3px solid #1583e9;
  background-color: #f0f0f0;
  margin-bottom: 15px;
  font-weight: 500;
  padding-left: 16px;
  line-height: 18px;
  padding-top: 4px;
  padding-bottom: 4px;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  position: relative;
  /* 为伪元素定位做准备 */
  border-left: none;
  /* 移除原生的左边框 */
}

.el-drawer__header {
  padding-top: 15px;
  padding-left: 5px;
  padding-bottom: 1px;
  margin-bottom: 1px
}

.el-drawer__header> :first-child {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  color: #333;
  font-weight: 700;
  font-size: 18px;
  // margin-left: 2px;
  font-family: Avenir, Helvetica, Arial, sans-serif;

}

.ellipsis {
  // max-width: 100px;
  overflow: hidden;
  white-space: nowrap;
  // text-overflow: ellipsis;
  display: inline-block;
}

.inputInfo {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon-document-copy {
    color: rgb(24, 144, 255);
  }

}

.el-tabs {
  margin-left: 10px;
}

.el-tabs__item {
  font-weight: bold;
}

/* 全局样式 */
.el-table .center-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 或者局部样式 */
.inputInfo.copy_icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-container-top {
  display: flex;
  justify-content: flex-end;
}

.app-container-bottom {
  display: flex;
  gap: 10px;
}