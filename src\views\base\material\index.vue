<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="75px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item label="是否批次件" prop="isBatch" label-width="100px" @keyup.enter.native="handleQuery">
          <el-select v-model="queryParams.isBatch" placeholder="请选择是否批次件" clearable>
            <el-option v-for="dict in dict.type.is_batch" :key="dict.materialTypeCode" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料类型" prop="materialTypeName">
          <el-select v-model="queryParams.materialType" placeholder="请选择物料类型" clearable>
            <el-option v-for="item in materialTypeList" :key="item.id" :label="item.materialTypeName"
              :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:material:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:material:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:material:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:material:export']">导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-upload" size="mini" @click="handleImport"
            v-hasPermi="['system:material:import']">导入</el-button>
        </el-col>

        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="materialList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="id" align="center" prop="id" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="物料版本" align="center" prop="materialVersion" />
        <el-table-column label="物料类型" align="center" prop="materialTypeName" />
        <el-table-column label="是否批次件" align="center" prop="isBatch">
          <template slot-scope="scope">
            {{ scope.row.isBatch == 1 ? "是" : "否" }}
          </template>
        </el-table-column>

        <!-- <el-table-column label="是否批次件" align="center" prop="isBatch" /> -->
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="物料属性" align="center" prop="materialAttribute" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="170">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-printer"
              @click="handlePrint(scope.row)"
              >打印</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:material:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:material:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- Excel导入对话框 -->
      <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xlsx、xls格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 添加或修改 物料对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物料编码" prop="materialCode" style="width: 240px">
                <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料名称" prop="materialName" style="width: 240px">
                <el-input v-model="form.materialName" placeholder="请输入物料名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="规格型号" prop="specification" style="width: 240px">
                <el-input v-model="form.specification" placeholder="请输入规格型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料版本" prop="materialVersion" style="width: 240px">
                <el-input v-model="form.materialVersion" placeholder="请输入物料版本" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- <el-form-item label="是否批次件" prop="isBatch">
            <el-input v-model="form.isBatch" placeholder="请输入是否批次件" />
          </el-form-item> -->

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物料类型" prop="materialType" style="width: 240px">
                <!-- <el-input v-model="form.materialType" placeholder="请输入物料类型" /> -->
                <el-select v-model="form.materialType" placeholder="请选择物料类型" clearable style="width: 240px">
                  <el-option v-for="item in materialTypeList" :key="item.id" :label="item.materialTypeName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="批次件" prop="isBatch" style="width: 240px">
                <el-select v-model="form.isBatch" placeholder="是否是批次件" clearable style="width: 240px">
                  <el-option v-for="dict in dict.type.is_batch" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="单位" prop="materialUnit" style="width: 240px">
                <el-select v-model="form.materialUnit" placeholder="请选择单位" clearable style="width: 240px">
                  <el-option v-for="item in unitList" :key="item.value" :label="item.value" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物料属性" prop="materialAttribute" style="width: 240px">
                <el-input v-model="form.materialAttribute" placeholder="请输入物料属性" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注" prop="remark" style="width: 720px">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio v-model="form.status" label="0">正常</el-radio>
            <el-radio v-model="form.status" label="1">停用</el-radio>
          </el-form-item>
          <!-- <el-form-item label="删除标志" prop="delFlag">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listMaterial,
  getMaterial,
  delMaterial,
  addMaterial,
  updateMaterial,
} from "@/api/system/material";
import { listMaterialType } from "@/api/system/materialType";
import { listUnit } from "@/api/system/unit";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { listHiprint } from "@/api/system/hiprint";

export default {
  name: "Material",
  dicts: ["is_batch"],
  data() {
    return {
      showMoreConditions: false, // 控制是否显示更多条件
      //单位下拉框
      unitList: [],
      // 物料类型下拉框数据
      materialTypeList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //  物料表格数据
      materialList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 上传参数
      upload: {
        open: false,
        title: '导入物料数据',
        url: process.env.VUE_APP_BASE_API + '/system/material/importData',
        headers: { Authorization: 'Bearer ' + this.$store.getters.token },
        isUploading: false
      },
      // 查询参数
      queryParams: {
        materialTypeList: null,
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        specification: null,
        materialVersion: null,
        materialType: null,
        isBatch: null,
        materialUnit: null,
        materialAttribute: null,
        status: null,
        comId: null,
        materialTypeCode: null,
        materialTypeName: null,
      },
      queryParamsType: {
        pageNum: 1,
        pageSize: 10000,
      },
      // 表单参数
      form: {},
      mypanel: {},
      // 表单校验
      rules: {
        materialCode: [
          { required: true, trigger: "blur", message: "物料编码不能为空" },
        ],
        materialName: [
          { required: true, trigger: "blur", message: "物料名称不能为空" },
        ],
        specification: [
          { required: true, trigger: "blur", message: "规格型号不能为空" },
        ],
        materialVersion: [
          { required: true, trigger: "blur", message: "物料版本不能为空" },
        ],
        isBatch: [
          { required: true, trigger: "blur", message: "请勾选是否为批次件" },
        ],
        materialType: [
          { required: true, trigger: "blur", message: "物料类型不能为空" },
        ],
        // picPhone: [
        //   // { required: true, message: "手机号码不能为空", trigger: "blur" },
        //   {
        //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        //     message: "请输入正确的手机号码",
        //     trigger: "blur",
        //   },
        // ],
      },
    };
  },
  created() {
    this.getList();
    this.queryMaterialType();
    this.getPrintList()
  },
  methods: {
    getPrintList() {
      listHiprint({ code: "material" }).then((response) => {
        this.mypanel = JSON.parse(response.rows[0].printJson);
      });
    },
    toggleMoreConditions() {
      this.showMoreConditions = !this.showMoreConditions; // 切换显示状态
    },
    /** 查询 物料列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      listMaterial(this.queryParams).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialTypeName: null,
        // materialTypeList: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialVersion: null,
        materialType: null,
        isBatch: null,
        materialUnit: null,
        materialAttribute: null,
        status: "0",
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    //打印按钮操作
    handlePrint(row) {
      hiprint.init();
      //调用接口获取数据
      var hiprintTemplate = new hiprint.PrintTemplate({
        template: this.mypanel,
        settingContainer: "#templateDesignDiv",
      });
      hiprintTemplate.print([row]);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加 物料";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMaterial(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改 物料";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMaterial(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaterial(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除 物料编号为"' + row.materialCode + '"的数据项？')
        .then(function () {
          // 将单个id转换为数组格式，以匹配后端控制器方法的参数要求
          const idsArray = Array.isArray(ids) ? ids : [ids];
          return delMaterial(idsArray.join(','));
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/material/export",
        {
          ...this.queryParams,
        },
        `物料信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      // 使用download方法，它会自动处理认证令牌
      this.download('system/material/importTemplate', {}, `物料导入模板.xlsx`);
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    /** 提交上传文件 */
    submitFileForm() {
      this.$refs.upload.submit();
    },
    queryMaterialType() {
      listMaterialType(this.queryParamsType).then((response) => {
        this.materialTypeList = response.rows;
      });
      listUnit(this.queryParamsType).then((response) => {
        this.unitList = response.rows;
      });
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.materialCode + '"物料吗？')
        .then(function () {
          return updateMaterial(row);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
  },
};
</script>
