<template>
  <div class="app-container">
    <div class="app-container-div">
      <!-- 查询部分 -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="90px"
      >
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="lineStateArr">
          <el-select
            @change="lineStateChange"
            multiple
            v-model="queryParams.lineStateArr"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.line_state_dict"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" style="margin-left: 2px">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:purchase:add']"
            >新增采购单</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:purchase:export']"
            >导出采购单</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <!-- 查询部分结束 -->

      <!-- 主列表开始了哈 -->
      <el-table
        height="60vh"
        v-loading="loading"
        :data="purchaseList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="采购单号"
          align="center"
          prop="purchaseNo"
          :width="tableWidth(purchaseList.map((x) => x.purchaseNo))"
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.purchaseNo"
              >
                <span class="ellipsis" style="display: inline-block">
                  {{ scope.row.purchaseNo }}</span
                >
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.purchaseNo"
                v-clipboard:success="onCopy"
              >
              </i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="供应商编码"
          align="center"
          prop="supplierCode"
        />
        <el-table-column
          label="供应商名称"
          align="center"
          prop="supplierName"
        />
        <el-table-column
          label="采购时间"
          align="center"
          prop="purchaseDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.purchaseDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="lineState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.line_state_dict"
              :value="scope.row.lineState"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
              @click="clickDetails(scope.row)"
              >详情</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-show="scope.row.lineState === 'CREATED'"
              v-hasPermi="['system:purchase:edit']"
              >修改</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleSubmit(scope.row)"
              v-show="scope.row.lineState === 'CREATED'"
              >保存</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <!-- 主列表结束了哈 -->

      <!-- 添加&修改时候的采购单弹出框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        height="10vh"
        :size="'75%'"
        append-to-body
      >
        <el-form
          :inline="true"
          :label-position="labelPosition"
          label-width="120px"
          ref="form"
          :model="form"
          :rules="rules"
          style="margin: 5px"
        >
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="采购单信息" name="1">
              <el-row :gutter="24" class="mb8">
                <el-col :span="8">
                  <el-form-item label="采购单号" prop="purchaseNo">
                    <el-input
                      icon=""
                      :disabled="form.id"
                      v-model="form.purchaseNo"
                      placeholder="请输入采购单号"
                      style="width: 220px"
                    >
                      <i
                        slot="suffix"
                        @click="getCodePurchase"
                        style="
                          color: #08a6e5;
                          font-weight: 900;
                          cursor: pointer;
                        "
                        class="el-input__icon el-icon-magic-stick"
                      ></i>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="采购日期" prop="purchaseDate">
                    <el-date-picker
                      clearable
                      v-model="form.purchaseDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择采购时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="供应商编码" prop="supplierCode">
                    <el-input
                      disabled
                      v-model="form.supplierCode"
                      placeholder="请输入供应商编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="供应商" prop="supplierName">
                    <el-select
                      @change="supplierNameChange"
                      v-model="form.supplierName"
                      placeholder="请选择供应商"
                      clearable
                    >
                      <el-option
                        v-for="item in supplierList"
                        :key="item.id"
                        :label="item.supplierName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="业务类型" prop="businessType">
                    <el-select
                      v-model="form.businessType"
                      placeholder="请选择业务类型"
                    >
                      <el-option
                        v-for="dict in dict.type.business_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="单据类型" prop="billType">
                    <el-select
                      v-model="form.billType"
                      placeholder="请选择单据类型"
                    >
                      <el-option
                        v-for="dict in dict.type.document_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>

            <!-- 下面是物料明细那些东西 新增/修改的时候出现的额 -->
            <el-collapse-item title="采购单明细信息" name="2">
              <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                  <el-button
                    style="margin-left: 6px"
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAddWmsErpPurchaseDetail"
                    >导入</el-button
                  >
                </el-col>
              </el-row>
              <el-table
                :data="wmsErpPurchaseDetailList"
                :row-class-name="rowWmsErpPurchaseDetailIndex"
                ref="wmsErpPurchaseDetail"
              >
                <el-table-column
                  label="序号"
                  align="center"
                  prop="index"
                  width="50"
                />
                <el-table-column label="物料编码" prop="partCode">
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.partCode"
                      placeholder=""
                    />
                  </template>
                </el-table-column>
                <el-table-column label="物料名称" prop="partName">
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.partName"
                      placeholder=""
                    />
                  </template>
                </el-table-column>
                <el-table-column label="物料版本" prop="partVersion">
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.partVersion"
                      placeholder=""
                    />
                  </template>
                </el-table-column>
                <el-table-column label="数量" prop="qty">
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.qty"
                      placeholder="请输入数量"
                      @blur="validateQty(scope.row)"
                    >
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="lineState">
                  <template slot-scope="scope">
                    <dict-tag
                      :options="dict.type.line_state_dict"
                      :value="scope.row.lineState"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="单位" prop="uom">
                  <template slot-scope="scope">
                    <el-input disabled v-model="scope.row.uom" placeholder="" />
                  </template>
                </el-table-column>
                <el-table-column label="规格型号" prop="partSpecification">
                  <template slot-scope="scope">
                    <el-input
                      disabled
                      v-model="scope.row.partSpecification"
                      placeholder=""
                    />
                  </template>
                </el-table-column>
                <el-table-column fixed="right" center label="操作" width="60">
                  <template slot-scope="scope">
                    <el-button
                      @click="deleteImport(scope.row)"
                      type="text"
                      size="small"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div style="display: flex; justify-content: end; margin-top: 5px">
          <el-button type="primary" @click="submitForm">提 交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 详情的vue代码 -->
      <el-drawer
        title="详情"
        v-loading="loading_detial"
        :visible.sync="detialOpen"
        :before-close="detailClose"
        :size="'75%'"
        append-to-body
      >
        <el-form
          :model="form"
          label-width="90px"
          size="small"
          :label-position="labelPosition"
          :inline="true"
        >
          <div>
            <el-form
              :inline="true"
              :label-position="labelPosition"
              label-width="120px"
              ref="form"
              :model="form"
              :rules="rules"
            >
              <el-collapse v-model="activeNamesInfo">
                <el-collapse-item title="采购单信息" name="1">
                  <el-row :gutter="24" class="mb8">
                    <el-col :span="8">
                      <el-form-item label="采购单号" prop="purchaseNo">
                        <el-input
                          disabled
                          v-model="form.purchaseNo"
                          placeholder=""
                          style="width: 220px"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="采购日期" prop="purchaseDate">
                        <el-date-picker
                          disabled
                          clearable
                          v-model="form.purchaseDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder=""
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label=" 状态" prop="lineState">
                        <el-select
                          disabled
                          v-model="form.lineState"
                          placeholder=""
                        >
                          <el-option
                            v-for="dict in dict.type.line_state_dict"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="供应商" prop="supplierName">
                        <el-select
                          disabled
                          @change="supplierNameChange"
                          v-model="form.supplierName"
                          placeholder=""
                          clearable
                        >
                          <el-option
                            v-for="item in supplierList"
                            :key="item.id"
                            :label="item.supplierName"
                            :value="item.id"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="供应商编码" prop="supplierCode">
                        <el-input
                          disabled
                          v-model="form.supplierCode"
                          placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="业务类型" prop="businessType">
                        <el-select
                          disabled
                          v-model="form.businessType"
                          placeholder=""
                        >
                          <el-option
                            v-for="dict in dict.type.business_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="单据类型" prop="billType">
                        <el-select
                          disabled
                          v-model="form.billType"
                          placeholder=""
                        >
                          <el-option
                            v-for="dict in dict.type.document_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-collapse-item>
              </el-collapse>
            </el-form>
          </div>
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="采购单明细信息" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="采购明细" name="second">
                  <el-table
                    height="50vh"
                    :data="wmsErpPurchaseDetailList"
                    :row-class-name="rowWmsErpPurchaseDetailIndex"
                    ref="wmsErpPurchaseDetail"
                  >
                    <el-table-column align="center" type="index" width="50" />
                    <el-table-column
                      align="center"
                      label="物料编码"
                      prop="partCode"
                      :width="
                        tableWidth(
                          wmsErpPurchaseDetailList.map((x) => x.partCode)
                        )
                      "
                    />
                    <el-table-column
                      align="center"
                      label="物料名称"
                      prop="partName"
                      :width="
                        tableWidth(
                          wmsErpPurchaseDetailList.map((x) => x.partName)
                        )
                      "
                    />
                    <el-table-column
                      align="center"
                      label="规格型号"
                      prop="partSpecification"
                    />
                    <el-table-column
                      label="状态"
                      align="center"
                      prop="lineState"
                    >
                      <template slot-scope="scope">
                        <dict-tag
                          :options="dict.type.line_state_dict"
                          :value="scope.row.lineState"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="数量" prop="qty">
                      <template slot-scope="scope">
                        {{ scope.row.qty ? scope.row.qty : "0" }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="已收数量"
                      prop="receivedNum"
                    >
                      <template slot-scope="scope">
                        {{
                          scope.row.receivedNum ? scope.row.receivedNum : "0"
                        }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      align="center"
                      label="已发数量"
                      prop="issuedNum"
                    >
                      <template slot-scope="scope">
                        {{ scope.row.issuedNum ? scope.row.issuedNum : "0" }}
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="单位" prop="uom" />
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
      <!-- 详情的vue代码 -->
      <!-- 点击新增订单详情dialog框 -->
      <el-dialog
        title="新增订单详情"
        :visible.sync="importWlVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form
          :inline="true"
          :model="wl_ruleForm"
          :rules="wl_rules"
          ref="wl_ruleForm"
          label-width="100px"
        >
          <el-form-item label="物料编码" prop="partCode">
            <el-input
              @focus="importFuction"
              placeholder="请输入物料编码"
              v-model="wl_ruleForm.partCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="物料名称" prop="partName">
            <el-input
              disabled
              v-model="wl_ruleForm.partName"
              placeholder="请输入物料名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="数量" prop="qty">
            <el-input
              v-model="wl_ruleForm.qty"
              placeholder="请输入数量"
            ></el-input>
          </el-form-item>
          <el-form-item label="物料版本" prop="partVersion">
            <el-input
              disabled
              v-model="wl_ruleForm.partVersion"
              placeholder="请输入物料版本"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位" prop="uom">
            <el-input
              disabled
              v-model="wl_ruleForm.uom"
              placeholder="请输入物料单位"
            ></el-input>
          </el-form-item>
          <el-form-item label="物料规格型号" prop="partSpecification">
            <el-input
              disabled
              placeholder="请输入物料规格型号"
              v-model="wl_ruleForm.partSpecification"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">返 回</el-button>
          <el-button type="primary" @click="importConfurim">导 入</el-button>
        </span>
      </el-dialog>
      <!-- 新增订单详情的dialog -->
      <!-- 物料列表dialog -->
      <el-dialog
        title="选择要导入的物料信息"
        :visible.sync="wlList_dialogVisible"
        width="80%"
      >
        <div class="input-group">
          <div class="input-item">
            <label>物料编码：</label>
            <el-input
              v-model="qur.materialCode"
              placeholder="请输入物料编码"
              clearable
              @keyup.enter.native="getMaterialList"
            ></el-input>
          </div>
          <div class="input-item">
            <label>物料名称：</label>
            <el-input
              v-model="qur.materialName"
              placeholder="请输入物料名称"
              clearable
              @keyup.enter.native="getMaterialList"
            ></el-input>
          </div>
          <div class="input-item">
            <el-button type="primary" size="small" @click="getMaterialList"
              >查询</el-button
            >
            <el-button type="primary" size="small" @click="restGetMaterialList"
              >重置</el-button
            >
          </div>
        </div>
        <el-table
          :data="materialList"
          @row-click="importData"
          style="width: 100%"
        >
          <el-table-column align="left" width="35" label="行" prop="id">
            <template slot-scope="scope">
              <el-checkbox
                :label="scope.$index"
                v-model="selectedRow"
                @change="importData(scope.row)"
                >{{ " " }}
              </el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            label="物料编码"
            align="center"
            prop="materialCode"
          />
          <el-table-column
            label="物料名称"
            align="center"
            prop="materialName"
          />
          <el-table-column
            label="规格型号"
            align="center"
            prop="specification"
          />
          <el-table-column
            label="物料版本"
            align="center"
            prop="materialVersion"
          />
          <el-table-column label="单位" align="center" prop="materialUnit" />
        </el-table>
        <!-- 添加分页组件 -->
        <pagination
          v-show="materialTotal > 0"
          :total="materialTotal"
          :page.sync="qur.pageNum"
          :limit.sync="qur.pageSize"
          @pagination="getMaterialList"
        />
      </el-dialog>
      <!-- 物料列表dialog -->
    </div>
  </div>
</template>
<script>
import { listPurchase_detail } from "@/api/system/purchase_detail";
import {
  listPurchase,
  getPurchase,
  delPurchase,
  addPurchase,
  updatePurchase,
} from "@/api/system/purchase";
import { genCode } from "@/api/autocode/rule";
import { listSupplier } from "@/api/system/supplier";
import { listMaterial } from "@/api/system/material";
export default {
  name: "Purchase",
  dicts: ["document_type", "line_state_dict", "stock_in_type", "business_type"],
  data() {
    return {
      trueForm: true,
      activeNames: "",
      //详情
      loading_detial: true,
      activeNamesInfo: ["1", "2"],
      detialOpen: false,
      activeName: "second",
      //单选框绑定的model
      selectedRow: null,
       materialTotal: 0,  // 添加物料总数
      qur: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        status: "0", // 物料状态
      },
      //在一堆物料列表当中点击导入后 暂存物料记录的表单，最终将会被push到 主表单from的采购明细列表
      wl_ruleForm: {
        id: "",
        partCode: "",
        partName: "",
        partVersion: "",
        uom: "",
        qty: "",
        partSpecification: "",
        minPackageNum: "",
      },
      //导入物料后 数量和为空，添加校验 防止用户直接点击确定但是数据为空的 rule
      wl_rules: {
        partCode: [
          { required: true, message: "请导入物料编码", trigger: "change" },
        ],
        qty: [{ required: true, message: "请填写物料数量", trigger: "change" }],
      },
      // 采购主界面点击新增的时候的dialog控制变量
      importWlVisible: false,
      // 物料详细列表加载数据
      detial_loading: false,
      // 添加的时候 表单input或者其他元素的排列方式 对齐方式
      labelPosition: "left",
      //供应山管理列表
      supplierList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsErpPurchaseDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单表格数据
      purchaseList: [],
      // 采购单明细表格数据
      wmsErpPurchaseDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        receivingWarehouse: null,
        billType: null,
        businessType: null,
        purchaseDate: null,
        supplierName: null,
        supplierCode: null,
        purchaseNo: null,
        stockInType: null,
        lineStateArr: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseNo: [{ required: true, message: "采购单号不能为空" }],
        purchaseDate: [{ required: true, message: "采购日期不能为空" }],
        supplierName: [{ required: true, message: "供应商不可以为空" }],
      },
      // 点击物料编码（或者聚焦input的时候）弹出：“选择要导入的物料信息”的dialog对话框
      wlList_dialogVisible: false,
      // 物料列表
      materialList: [],
    };
  },
  created() {
    // 查询采购单列表
    this.getList();
    // 供应商管理列表
    this.getSupplierList();
  },
  methods: {
    // 数量校验
    validateQty(row) {
      const value = row.qty;
      this.trueForm = false;
      if (
        value === undefined ||
        value === null ||
        (typeof value === "string" && value.trim() === "")
      ) {
        this.$modal.msgError("添加的明细数量不得为空");

        return;
      }
      const numValue = Number(value);
      if (isNaN(numValue)) {
        this.$modal.msgError("不合法的数量，请输入数字");
        return;
      }
      if (numValue <= 0) {
        this.$modal.msgError("添加的明细数量必须大于0");
        return;
      }
      // 检查是否包含非数字字符（除了开头的负号和小数点）
      if (/[^0-9.]/.test(String(value).replace(/^-/, ""))) {
        this.$modal.msgError("不合法的数量，请输入数字");
        return;
      }

      this.trueForm = true;
    },

    //获取采购单号从后台
    getCodePurchase() {
      if (this.form.purchaseNo) {
        return;
      }
      genCode("purchase_no").then((response) => {
        this.form.purchaseNo = response;
      });
    },
    //tabs点击别的时候，暂时没用到
    handleClick() {},
    //详情取消的时候
    detailClose() {
      this.detialOpen = false;
    },
    //点击详情的时候
    clickDetails(row) {
      this.loading_detial = true;
      this.detialOpen = true;
      const id = row.id || this.ids;
      this.detail_list(row);
      getPurchase(id).then((response) => {
        this.form = response.data;
      });
    },
    //多选不同的查询状态的时候
    lineStateChange(val) {},

    //修改的时候点击某一行 删除的时候
    deleteImport(row) {
      const partId = row.partId;
      this.wmsErpPurchaseDetailList = this.wmsErpPurchaseDetailList.filter(
        (item) => item.partId !== partId
      );
    },

    handleRadioChange(row) {},
    // 新增详情点确定的方法
    importConfurim() {
      this.$refs["wl_ruleForm"].validate((valid) => {
        if (valid) {
          this.importWlVisible = false;
          let item = this.wl_ruleForm;
          let obj = {};
          obj.id = item.id;
          obj.returnQty = "";
          obj.materialReceivingUnit = "";
          obj.purchaseLine = "";
          obj.materialStage = "";
          obj.minPackageNum = item.minPackageNum;
          obj.lineState = "CREATED";
          obj.receivedNum = "";
          obj.issuedNum = "";
          obj.uom = item.uom;
          obj.receivingWarehouse = "";
          obj.partSpecification = item.partSpecification;
          obj.qty = item.qty;
          obj.partCode = item.partCode;
          obj.partVersion = item.partVersion;
          obj.partName = item.partName;
          obj.purchaseOrderLineId = "";
          obj.partId = item.partId;
          this.wmsErpPurchaseDetailList.push(obj);
          this.rest_importForm();
        } else {
        }
      });
    },
    //用户点击导入的时候的方法
    importData(row, e, i) {
      const isTrue = this.wmsErpPurchaseDetailList.find(
        (item) => item.id === row.id || row.id === item.partId
      );
      if (isTrue) {
        // 判断isTrue.hint是否为1，避免不必要的复杂判断
        if (isTrue.hint === 1) {
          return;
        }
        this.$modal.msgError("采购明细中已存在该物料!");
        // 明确设置hint的值
        return (isTrue.hint = 1);
      }
      this.rest_importForm();
      (this.wl_ruleForm = {
        id: row.id,
        //物料编码
        partCode: row.materialCode,
        // 物料名称
        partName: row.materialName,
        //物料版本
        partVersion: row.materialVersion,
        //物料单位
        uom: row.materialUnit,
        //物料数量
        qty: null,
        //物料规格型号
        partSpecification: row.specification,
        //最小包装数
        // minPackageNum: null,
        //物料id
        partId: row.id,
      }),
        (this.wlList_dialogVisible = false);
      this.selectedRow = null;
    },
    //重置导入表内容
    rest_importForm() {
      this.wl_ruleForm = {
        partCode: "",
        partName: "",
        partVersion: "",
        uom: "",
        qty: "",
        partSpecification: "",
        minPackageNum: "",
      };
    },
    // 重置查询
    restGetMaterialList() {
      this.qur = {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        status: "0"
      };
      this.getMaterialList();
    },
    /** 查询 物料列表 */
    getMaterialList() {
      this.qur.status = "0"; // 物料状态
      listMaterial(this.qur).then((response) => {
        this.materialList = response.rows;
        this.materialTotal = response.total;
      });
    },
    // 物料编码聚焦点的时候方法
    importFuction() {
      this.wlList_dialogVisible = true;
      this.getMaterialList();
    },
    //
    // 点击新增以后 又取消了的时候的方法
    handleClose(done) {
      this.importWlVisible = false;
      this.rest_importForm();
    },
    /** 查询采购单明细列表 */
    detail_list(row) {
      let queryParams = {
        purchaseNo: row.purchaseNo,
      };
      listPurchase_detail(queryParams).then((response) => {
        this.wmsErpPurchaseDetailList = response.rows;
        this.loading_detial = false;
      });
    },

    //供应商选择改变的时候
    supplierNameChange(id) {
      const item = this.supplierList.find((item) => {
        return item.id == id;
      });
      if (item) {
        this.form.supplierCode = item.supplierCode;
        this.form.supplierName = item.supplierName;
        this.form.supplierId = item.id;
      }
    },
    /** 查询 供应商管理列表 */
    getSupplierList() {
      listSupplier(this.queryParams).then((response) => {
        this.supplierList = response.rows;
      });
    },
    /** 查询采购单列表 */
    getList() {
      this.loading = true;
      listPurchase(this.queryParams).then((response) => {
        this.purchaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        receivingWarehouse: null,
        billType: null,
        businessType: null,
        lineState: null,
        supplierId: null,
        purchaseDate: null,
        stockInType: null,
        date: null,
        closeDate: null,
        purchaseOrderId: null,
        fcloserId: null,
        purchaserId: null,
        closeStatus: null,
        cancelStatus: null,
        purchaseDeptId: null,
        purchaserGroupId: null,
        purchaseOrgId: null,
        sourceType: null,
        supplierName: null,
        supplierCode: null,
        purchaseNo: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsErpPurchaseDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");

      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.lineState = "CREATED";

      this.title = "添加采购单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      const id = row.id || this.ids;
      getPurchase(id).then((response) => {
        this.form = response.data;
        this.wmsErpPurchaseDetailList = response.data.wmsErpPurchaseDetailList;
        this.open = true;
        this.title = "修改采购单";
      });
    },
    editImport(row) {
      // this.reset();
      const id = row.id || this.ids;
      getMaterial(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改 物料";
      });
    },
    /**表格的提交，更改状态 */
    handleSubmit(row) {
      getPurchase(row.id).then((response) => {
        row.wmsErpPurchaseDetailList = response.data.wmsErpPurchaseDetailList;
        const submitData = {
          ...row,
          lineState: "EXECUTING", // 更新状态为执行中
        };
        // 调用更新API
        updatePurchase(submitData)
          .then((res) => {
            this.$modal.msgSuccess("保存成功");
            // 刷新列表数据
            this.getList();
          })
          .catch((error) => {
            console.error("保存失败:", error);
            this.$modal.msgError("保存失败");
          })
          .finally(() => {});
      });
    },

    // 提交
    submitForm() {
      // 1. 检查是否有物料
      if (this.wmsErpPurchaseDetailList.length === 0) {
        this.$modal.msgError("请至少添加一项物料信息！");
        return;
      }
      // 2. 校验所有明细行的数量
      this.wmsErpPurchaseDetailList.forEach((row) => {
        this.validateQty(row);
      });
      // 原始提交逻辑
      this.$refs["form"].validate((valid) => {
        if (valid && this.trueForm) {
          this.form.wmsErpPurchaseDetailList = this.wmsErpPurchaseDetailList;
          if (this.form.id != null) {
            updatePurchase(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPurchase(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          console.error("校验不通过");
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除采购单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delPurchase(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 采购单明细序号 */
    rowWmsErpPurchaseDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 采购单明细添加按钮操作 */
    handleAddWmsErpPurchaseDetail() {
      this.importWlVisible = true;
    },
    /** 采购单明细删除按钮操作 */
    handleDeleteWmsErpPurchaseDetail() {
      if (this.checkedWmsErpPurchaseDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的采购单明细数据");
      } else {
        const wmsErpPurchaseDetailList = this.wmsErpPurchaseDetailList;
        const checkedWmsErpPurchaseDetail = this.checkedWmsErpPurchaseDetail;
        this.wmsErpPurchaseDetailList = wmsErpPurchaseDetailList.filter(
          function (item) {
            return checkedWmsErpPurchaseDetail.indexOf(item.index) == -1;
          }
        );
      }
    },
    /** 复选框选中数据 */
    handleWmsErpPurchaseDetailSelectionChange(selection) {
      this.checkedWmsErpPurchaseDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/purchase/export",
        {
          ...this.queryParams,
        },
        `purchase_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style scoped>
/* 整体容器样式 */
.input-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: start;
}

/* 每个输入组的样式 */
.input-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-right: 10px;
}

/* 标签样式 */
.input-item label {
  width: 100px;
  text-align: right;
  margin-right: 10px;
}

/* 输入框样式 */
.input-item input {
  flex: 1;
}

.el-form {
  padding: 8px 0 !important;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

/* 新增错误提示样式
.error-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
} */
/* 错误提示样式 */
.error-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: relative;
  left: 0;
  top: -5px;
}
</style>
