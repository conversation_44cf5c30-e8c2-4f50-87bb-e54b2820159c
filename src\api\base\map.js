import request from '@/utils/request'

// 查询仓库物料关系列表
export function listMap(query) {
  return request({
    url: '/system/map/list',
    method: 'get',
    params: query
  })
}

// 查询仓库物料关系详细
export function getMap(id) {
  return request({
    url: '/system/map/' + id,
    method: 'get'
  })
}

// 新增仓库物料关系
export function addMap(data) {
  return request({
    url: '/system/map',
    method: 'post',
    data: data
  })
}

// 修改仓库物料关系
export function updateMap(data) {
  return request({
    url: '/system/map',
    method: 'put',
    data: data
  })
}

// 删除仓库物料关系
export function delMap(id) {
  return request({
    url: '/system/map/' + id,
    method: 'delete'
  })
}
