import request from '@/utils/request'

// 查询调拨单明细列表
export function listAllotDetail(query) {
  return request({
    url: '/system/allotDetail/list',
    method: 'get',
    params: query
  })
}

// 查询调拨单明细详细
export function getAllotDetail(id) {
  return request({
    url: '/system/allotDetail/' + id,
    method: 'get'
  })
}

// 新增调拨单明细
export function addAllotDetail(data) {
  return request({
    url: '/system/allotDetail',
    method: 'post',
    data: data
  })
}

// 修改调拨单明细
export function updateAllotDetail(data) {
  return request({
    url: '/system/allotDetail',
    method: 'put',
    data: data
  })
}

// 删除调拨单明细
export function delAllotDetail(id) {
  return request({
    url: '/system/allotDetail/' + id,
    method: 'delete'
  })
}
