<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-select v-model="queryParams.materialCode" placeholder="请选择物料编码" clearable filterable
            @change="handleQueryMaterialChange">
            <el-option v-for="item in materialList" :key="item.id" :label="item.materialCode"
              :value="item.materialCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-select v-model="queryParams.warehouseId" placeholder="请选择仓库" clearable filterable
            @change="handleQueryWarehouseChange">
            <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <el-button type="text" icon="el-icon-arrow-down" size="mini" @click="toggleAdvancedSearch">
            {{ isAdvancedSearchVisible ? '收起' : '展开' }}更多条件
          </el-button>
        </el-form-item>

        <!-- 高级搜索区域 -->
        <template v-if="isAdvancedSearchVisible">
          <el-form-item label="仓库编码" prop="warehouseCode">
            <el-input v-model="queryParams.warehouseCode" placeholder="请输入仓库编码" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="库区编码" prop="areaCode">
            <el-input v-model="queryParams.areaCode" placeholder="请输入库区编码" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="库区名称" prop="areaName">
            <el-input v-model="queryParams.areaName" placeholder="请输入库区名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="保质期" prop="expirationDate">
            <el-date-picker clearable v-model="queryParams.expirationDate" type="date" value-format="yyyy-MM-dd"
              placeholder="请选择保质期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="安全库存" prop="safetyStock">
            <el-input v-model="queryParams.safetyStock" placeholder="请输入安全库存" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="最大库存" prop="maxStock">
            <el-input v-model="queryParams.maxStock" placeholder="请输入最大库存" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="最小库存" prop="minStock">
            <el-input v-model="queryParams.minStock" placeholder="请输入最小库存" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="呆滞时间" prop="minPackageNum">
            <el-input v-model="queryParams.minPackageNum" placeholder="请输入呆滞时间" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="组织" prop="comId">
            <el-input v-model="queryParams.comId" placeholder="请输入组织" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
        </template>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:map:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:map:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:map:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:map:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="mapList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode"
          :width="tableWidth(mapList.map((x) => x.materialCode))" />
        <el-table-column label="物料名称" align="center" prop="materialName"
          :width="tableWidth(mapList.map((x) => x.materialName))" />
        <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
        <el-table-column label="库区编码" align="center" prop="areaCode" />
        <el-table-column label="库区名称" align="center" prop="areaName" />
        <el-table-column label="保质期" align="center" prop="expirationDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="安全库存" align="center" prop="safetyStock" />
        <el-table-column label="最大库存" align="center" prop="maxStock" />
        <el-table-column label="最小库存" align="center" prop="minStock" />
        <el-table-column label="呆滞时间" align="center" prop="minPackageNum" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:map:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:map:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改仓库物料关系对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'60%'" append-to-body>
        <el-form ref="form" :label-position="labelPosition" label-width="90px" :model="form" :rules="rules"
          >
          <el-collapse v-model="activeNames">
            <el-collapse-item title="仓库物料关系信息" name="1">
              <!-- 第一行 -->
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="物料编码" prop="materialCode">
                    <el-select v-model="form.materialCode" placeholder="请选择物料编码" @change="handleDialogMaterialChange" 
                      filterable clearable>
                      <el-option v-for="item in materialList" :key="item.id" :label="item.materialCode"
                        :value="item.materialCode" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="物料名称" prop="materialName">
                    <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="仓库名称" prop="warehouseName">
                    <el-select v-model="form.warehouseId" placeholder="请选择仓库" @change="handleWarehouseChange" filterable
                      clearable>
                      <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName"
                        :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 第二行 -->
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="仓库编码" prop="warehouseCode">
                    <el-input v-model="form.warehouseCode" placeholder="请输入仓库编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="库区编码" prop="areaCode">
                    <el-input v-model="form.areaCode" placeholder="请输入库区编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="库区名称" prop="areaName">
                    <el-input v-model="form.areaName" placeholder="请输入库区名称" />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 第三行 -->
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="保质期" prop="expirationDate">
                    <el-date-picker clearable v-model="form.expirationDate" type="date" value-format="yyyy-MM-dd"
                      placeholder="请选择保质期" style="width: 100%">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="安全库存" prop="safetyStock">
                    <el-input v-model="form.safetyStock" placeholder="请输入安全库存" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="最大库存" prop="maxStock">
                    <el-input v-model="form.maxStock" placeholder="请输入最大库存" />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 第四行 -->
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="最小库存" prop="minStock">
                    <el-input v-model="form.minStock" placeholder="请输入最小库存" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="呆滞时间" prop="minPackageNum">
                    <el-input v-model="form.minPackageNum" placeholder="请输入呆滞时间" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <!-- <el-form-item label="组织" prop="comId">
                    <el-input v-model="form.comId" placeholder="请输入组织" />
                  </el-form-item> -->
                </el-col>
              </el-row>

              <!-- 第五行 -->
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
          </el-collapse>



        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { listMap, getMap, delMap, addMap, updateMap } from "@/api/base/map";
import { listMaterial } from "@/api/system/material";
import { listWarehouse } from "@/api/system/warehouse";

export default {
  name: "Map",
  data() {
    return {
      activeNames: ["1"],
      labelPosition: "right",
      // 是否显示高级搜索
      isAdvancedSearchVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 仓库物料关系表格数据
      mapList: [],
      //物料编码数组
      codes: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialCode: null,
        materialName: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        expirationDate: null,
        safetyStock: null,
        maxStock: null,
        minStock: null,
        minPackageNum: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" }
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" }
        ],
        warehouseId: [
          { required: true, message: "仓库不能为空", trigger: "blur" }
        ],
        warehouseCode: [
          { required: true, message: "仓库编码不能为空", trigger: "blur" }
        ],
        warehouseName: [
          { required: true, message: "仓库名称不能为空", trigger: "blur" }
        ],
        areaId: [
          { required: true, message: "库区不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "库区编码不能为空", trigger: "blur" }
        ],
        areaName: [
          { required: true, message: "库区名称不能为空", trigger: "blur" }
        ],
        expirationDate: [
          { required: true, message: "保质期不能为空", trigger: "blur" }
        ],
        safetyStock: [
          { required: true, message: "安全库存不能为空", trigger: "blur" }
        ],
        maxStock: [
          { required: true, message: "最大库存不能为空", trigger: "blur" }
        ],
        minStock: [
          { required: true, message: "最小库存不能为空", trigger: "blur" }
        ],
        minPackageNum: [
          { required: true, message: "呆滞时间不能为空", trigger: "blur" }
        ],

      },
      materialList: [], // 物料列表
      warehouseList: [], // 仓库列表
    };
  },
  created() {
    this.getList();
    this.getMaterialList(); // 加载物料列表
    this.getWarehouseList(); // 加载仓库列表
  },
  methods: {
    /** 查询仓库物料关系列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      listMap(this.queryParams).then(response => {
        this.mapList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        expirationDate: null,
        safetyStock: null,
        maxStock: null,
        minStock: null,
        minPackageNum: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.codes = selection.map(item => item.materialCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加仓库物料关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMap(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改仓库物料关系";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMap(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMap(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.materialCode || this.codes;
      this.$modal.confirm('是否确认删除物料编码为"' + codes + '"的数据项？').then(function () {
        return delMap(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/map/export', {
        ...this.queryParams
      }, `仓库物料关系_${new Date().toLocaleDateString()}.xlsx`)
    },
    /** 获取物料列表 */
    getMaterialList() {
      listMaterial({ pageSize: 10000 }).then(response => {
        this.materialList = response.rows;
      });
    },
    /** 获取仓库列表 */
    getWarehouseList() {
      listWarehouse({ pageSize: 10000 }).then(response => {
        this.warehouseList = response.rows;
      });
    },
    /** 物料选择改变 */
    handleMaterialChange(value) {
      const material = this.materialList.find(item => item.id === value);
      if (material) {
        this.form.materialId = material.id;
        this.form.materialName = material.materialName;
        this.form.materialCode = material.materialCode;
      }
    },
    /** 仓库选择改变 */
    handleWarehouseChange(value) {
      const warehouse = this.warehouseList.find(item => item.id === value);
      if (warehouse) {
        this.form.warehouseId = warehouse.id;
        this.form.warehouseCode = warehouse.warehouseCode;
        this.form.warehouseName = warehouse.warehouseName;
      }
    },
    /** 查询表单物料选择改变 */
    handleQueryMaterialChange(value) {
      const material = this.materialList.find(item => item.materialCode === value);
      if (material) {
        this.queryParams.materialId = material.id;
        this.queryParams.materialName = material.materialName;
      }
    },
    /** 弹出框物料选择改变 */
    handleDialogMaterialChange(value) {
      const material = this.materialList.find(item => item.materialCode === value);
      if (material) {
        this.form.materialId = material.id;
        this.form.materialName = material.materialName;
      }
    },
    /** 查询表单仓库选择改变 */
    handleQueryWarehouseChange(value) {
      const warehouse = this.warehouseList.find(item => item.id === value);
      if (warehouse) {
        this.queryParams.warehouseId = warehouse.id;
        this.queryParams.warehouseCode = warehouse.warehouseCode;
        this.queryParams.warehouseName = warehouse.warehouseName;
      }
    },
    /** 切换高级搜索 */
    toggleAdvancedSearch() {
      this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
    }
  }
};
</script>
