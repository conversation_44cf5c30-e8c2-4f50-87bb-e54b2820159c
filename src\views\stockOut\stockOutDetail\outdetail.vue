<template>
  <div class="">
    <div class="">
      <el-form
        v-show="activeName == 'first'"
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <!-- <el-table
        :style="{ cursor: activeName != 'first' ? 'pointer' : 'default' }"
        @row-click="sendDetailId"
        height="52vh"
        v-loading="loading"
        :data="stock_detailList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="activeName == 'first'"
          width="55"
          align="center"
          prop="index"
        />
        <el-table-column label="物料编号" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          v-if="activeName == 'first'"
          width="100"
          label="物料规格型号"
          align="center"
          prop="specification"
        />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="已入数量" align="center" prop="incomingQty" />
        <el-table-column
          v-if="activeName == 'first'"
          label="单位"
          align="center"
          prop="materialUnit"
        />
        <el-table-column
          v-if="activeName == 'first'"
          label="状态"
          align="center"
          prop="stockInState"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_in_state"
              :value="scope.row.stockInState"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeName == 'first'"
          label="质检状态"
          align="center"
          prop="qualityState"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.quality_state"
              :value="scope.row.qualityState"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeName == 'first'"
          label="合格数量"
          align="center"
          prop="qualifiedQty"
        />
        <el-table-column label="厂批次" align="center" prop="batchNo" />
        <el-table-column
          v-if="activeName == 'first'"
          label="驳回原因"
          align="center"
          prop="rejectReason"
        />
        <el-table-column label="加急状态" align="center" prop="urgentStatus" />
        <el-table-column
          v-if="activeName == 'first'"
          label="操作"
          width="70"
          align="center"
          class-name="small-padding fixed-width"
        >
        </el-table-column>
      </el-table> -->
      <el-table
        height="62vh"
        v-loading="loading"
        :data="stockOutDetailList"
        @selection-change="handleSelectionChange"
        @row-click="sendDetailId"
      >
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="" align="center" prop="id" /> -->
        <!-- <el-table-column
          label="采购单明细id"
          align="center"
          prop="purchaseDetailId"
        /> -->
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          v-if="activeName == 'first'"
          label="规格型号"
          align="center"
          prop="specification"
        />
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="已出数量" align="center" prop="incomingQty" />
        <!-- <el-table-column label="行号" align="center" prop="stockOutLine" /> -->
        <el-table-column
          label="出库状态"
          align="center"
          prop="stockOutState"
          v-if="activeName == 'first'"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_out_state"
              :value="scope.row.stockOutState"
            />
          </template>
        </el-table-column>
        <el-table-column label="质检状态" align="center" prop="qualityState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.quality_state"
              :value="scope.row.qualityState"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeName == 'first'"
          label="合格数量"
          align="center"
          prop="qualifiedQty"
        />
        <!-- <el-table-column
          v-if="activeName == 'first'"
          label="合格数量"
          align="center"
          prop="qualifiedQty"
        />
        <el-table-column
          v-if="activeName == 'first'"
          label="批次"
          align="center"
          prop="batchNo"
        />
        <el-table-column
          v-if="activeName == 'first'"
          label="备注"
          align="center"
          prop="remark"
        />
        <el-table-column
          v-if="activeName == 'first'"
          label="组织"
          align="center"
          prop="comId"
        /> -->
        <!-- <el-table-column label="出库主表id" align="center" prop="stockOutId" /> -->
        <!-- <el-table-column
          v-if="activeName == 'first'"
          label="出库单号"
          align="center"
          prop="stockOutNo"
        /> -->
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  listStockOutDetail,
  getStockOutDetail,
  addStockOutDetail,
  updateStockOutDetail,
  delStockOutDetail,
} from "@/api/system/stockOutDetail";

export default {
  props: ["stock_out_id", "activeName"],
  name: "componet_name",
  dicts: ["stock_out_type", "stock_out_state", "quality_state"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockInBox: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入库单明细表格数据
      stockOutDetailList: [],
      // 入库单箱信息表格数据
      wmsStockInBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // activeName: "first",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        incomingQty: null,
        stockOutLine: null,
        stockOutState: null,
        qualityState: null,
        qualifiedQty: null,
        batchNo: null,
        comId: null,
        stockOutId: this.stock_out_id,
        stockOutNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  // props: {
  //   tableData: {
  //     type: Array,
  //     default: () => [],
  //   },
  //   stock_out_id: {
  //     type: String,
  //     default: null,
  //   },
  // },
  created() {
    this.getList();
    console.log("stock_out_id", this.stock_out_id);
    console.log("wmsStockOutDetailList", this.wmsStockOutDetailList);
    console.log("activeName", this.stock_out_id);
  },
  methods: {
    sendDetailId(row, colom, env) {
      this.$emit("sendDetailId", row.id);
      console.log("row");
    },
    // handleClick(row){

    // },
    /** 查询入库单明细列表 */
    getList() {
      this.loading = true;

      listStockOutDetail(this.queryParams).then((response) => {
        this.stockOutDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        purchaseDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        incomingQty: null,
        stockInLine: null,
        stockInState: null,
        qualityState: null,
        qualifiedQty: null,
        batchNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        stockInId: null,
        stockInNo: null,
      };
      this.wmsStockInBoxList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加入库单明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStockOutDetail(id).then((response) => {
        this.form = response.data;
        this.wmsStockInBoxList = response.data.wmsStockInBoxList;
        this.open = true;
        this.title = "修改入库单明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsStockInBoxList = this.wmsStockInBoxList;
          if (this.form.id != null) {
            updateStockOutDetail(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStockOutDetail(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除入库单明细编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStockOutDetail(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 入库单箱信息序号 */
    rowWmsStockInBoxIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 入库单箱信息添加按钮操作 */
    handleAddWmsStockInBox() {
      let obj = {};
      obj.stockInId = "";
      obj.stockInNo = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.boxNo = "";
      obj.qrCode = "";
      obj.warehouseId = "";
      obj.warehouseCode = "";
      obj.warehouseName = "";
      obj.areaId = "";
      obj.areaCode = "";
      obj.areaName = "";
      obj.locationId = "";
      obj.locationCode = "";
      obj.locationName = "";
      obj.batchNo = "";
      obj.isStaging = "";
      obj.remark = "";
      obj.comId = "";
      obj.dateCode = "";
      obj.expirationDate = "";
      this.wmsStockInBoxList.push(obj);
    },
    /** 入库单箱信息删除按钮操作 */
    handleDeleteWmsStockInBox() {
      if (this.checkedWmsStockInBox.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单箱信息数据");
      } else {
        const wmsStockInBoxList = this.wmsStockInBoxList;
        const checkedWmsStockInBox = this.checkedWmsStockInBox;
        this.wmsStockInBoxList = wmsStockInBoxList.filter(function (item) {
          return checkedWmsStockInBox.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsStockInBoxSelectionChange(selection) {
      this.checkedWmsStockInBox = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stock_detail/export",
        {
          ...this.queryParams,
        },
        `入库单明细_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
