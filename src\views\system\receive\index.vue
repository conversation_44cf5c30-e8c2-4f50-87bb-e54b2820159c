<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="创建日期" prop="daterangeA">
          <el-date-picker size="small" style="min-width: none; max-width: 200px" value-format="yyyy-MM-dd"
            v-model="queryParams.daterangeA" type="daterange" range-separator="-" start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8" style="margin-left: 2px;">
        <el-col :span="1.5 ">
          <el-button v-show="false" type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:receive:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-show="false" type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
            @click="handleUpdate" v-hasPermi="['system:receive:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-show="false" type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
            @click="handleDelete" v-hasPermi="['system:receive:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:receive:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="60vh" v-loading="loading" :data="receiveList" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />

        <!-- <el-table-column label="收货单号" align="center" prop="receiveNo" :show-overflow-tooltip="true" /> -->

        <el-table-column label="收货单号" align="center" prop="receiveNo"
          :width="tableWidth(receiveList.map((x) => x.receiveNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip placement="top" effect="dark" :content="scope.row.receiveNo">
                <span class="ellipsis" style="display: inline-block">
                  {{ scope.row.receiveNo }}</span>
              </el-tooltip>
              <i style="margin-left: 10px; cursor: pointer" class="el-icon-document-copy"
                v-clipboard:copy="scope.row.receiveNo" v-clipboard:success="onCopy">
              </i>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="采购单号" align="center" prop="purchaseNo" :show-overflow-tooltip="true" />
        <el-table-column label="供应商编码" align="center" prop="supplierCode" :show-overflow-tooltip="true" />
        <el-table-column label="供应商名称" align="center" prop="supplierName" :show-overflow-tooltip="true" />
        <el-table-column label="收货状态" align="center" prop="receiveState" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
        <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="200px" /> -->
        <el-table-column label="操作" align="center" prop="createTime" width="200px">
          <template slot-scope="scope">
            <el-button icon="el-icon-s-order" type="text" size="small" @click="clickDetails(scope.row)">详情</el-button>

          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />


      <!-- 添加或修改收货单对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-row :gutter="24" class="mb8">
            <el-col :span="8">
              <el-form-item label="收货单号" prop="receiveNo">
                <el-input v-model="form.receiveNo" placeholder="请输入收货单号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="采购单号" prop="purchaseNo">
                <el-input v-model="form.purchaseNo" placeholder="请输入采购单号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商编码" prop="supplierCode">
                <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商名称" prop="supplierName">
                <el-input v-model="form.supplierName" placeholder="请输入供应商名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收货状态" prop="receiveState">
                <el-input v-model="form.receiveState" placeholder="请输入收货状态" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="组织" prop="comId">
                <el-input v-model="form.comId" placeholder="请输入组织" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="center">收货单明细信息</el-divider>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini"
                @click="handleAddWmsReceiveDetail">添加</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" icon="el-icon-delete" size="mini"
                @click="handleDeleteWmsReceiveDetail">删除</el-button>
            </el-col>
          </el-row>
          <el-table max-height="60vh" :data="wmsReceiveDetailList" :row-class-name="rowWmsReceiveDetailIndex"
            @selection-change="handleWmsReceiveDetailSelectionChange" ref="wmsReceiveDetail">
            <el-table-column align="center" type="index" width="50" />
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column type="selection" width="50" align="center" />

            <el-table-column label="收货单号" prop="receiveNo" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.receiveNo" placeholder="请输入收货单号" />
              </template>
            </el-table-column>
            <el-table-column label="采购单号" prop="purchaseNo" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.purchaseNo" placeholder="请输入采购单号" />
              </template>
            </el-table-column>
            <el-table-column label="物料编码" prop="materialCode" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialCode" placeholder="请输入物料编码" />
              </template>
            </el-table-column>
            <el-table-column label="物料名称" prop="materialName" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" />
              </template>
            </el-table-column>
            <el-table-column label="规格型号" prop="specification" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.specification" placeholder="请输入规格型号" />
              </template>
            </el-table-column>
            <el-table-column label="单位" prop="materialUnit" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.materialUnit" placeholder="请输入单位" />
              </template>
            </el-table-column>
            <el-table-column label="数量-总数" prop="qty" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.qty" placeholder="请输入数量-总数" />
              </template>
            </el-table-column>
            <el-table-column label="行号" prop="receiveLine" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.receiveLine" placeholder="请输入行号" />
              </template>
            </el-table-column>
            <el-table-column label="已收数量" prop="receiveQty" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.receiveQty" placeholder="请输入已收数量" />
              </template>
            </el-table-column>
            <el-table-column label="组织" prop="comId" width="150">
              <template slot-scope="scope">
                <el-input v-model="scope.row.comId" placeholder="请输入组织" />
              </template>
            </el-table-column>
          </el-table>
        </el-form>
        <div class="drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 详情页 -->
      <el-drawer title="详情" :visible.sync="detialOpen" :before-close="detailClose" :size="'75%'" append-to-body>
        <el-form :model="form" label-width="90px" size="small" :label-position="labelPosition" :inline="true">
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="收货单信息" name="1">
              <div style="margin: 10px;">
                <el-row :gutter="24" class="mb8">
                  <el-col :span="8">
                    <el-form-item label="收货单号" prop="receiveNo">
                      <el-input disabled v-model="form.receiveNo" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="采购单号" prop="purchaseNo">
                      <el-input disabled v-model="form.purchaseNo" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="供应商编码" prop="supplierCode">
                      <el-input disabled v-model="form.supplierCode" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="供应商名称" prop="supplierName">
                      <el-input disabled v-model="form.supplierName" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="收货状态" prop="receiveState">
                      <el-input disabled v-model="form.receiveState" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="组织" prop="comId">
                      <el-input disabled v-model="form.comId" placeholder="" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input disabled v-model="form.remark" placeholder="" />
                    </el-form-item>
                  </el-col>
                </el-row>

              </div>
            </el-collapse-item>
            <el-collapse-item title="收货单明细" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">

                <el-tab-pane label="收货明细" name="first">

                  <el-table height="50vh" :data="receiveList_DetailList" ref="wmsReceiveDetail">
                    <!-- <el-table-column fixed="left" type="index" width="50" label="序号" align="center" /> -->

                    <el-table-column fixed="left" label="采购单号" prop="purchaseNo"> </el-table-column>
                    <el-table-column label="行号" prop="receiveLine"> </el-table-column>
                    <el-table-column label="物料编码" prop="materialCode">
                    </el-table-column>
                    <el-table-column label="物料名称" prop="materialName"
                      :width="tableWidth(receiveList_DetailList.map(x => { x.materialName }))">
                    </el-table-column>
                    <el-table-column label="单位" prop="materialUnit"> </el-table-column>
                    <el-table-column label="规格型号" prop="specification">
                    </el-table-column>
                    <el-table-column label="已收数量" prop="receiveQty"> </el-table-column>
                    <el-table-column label="数量-总数" prop="qty"> </el-table-column>
                  </el-table>

                </el-tab-pane>
                <el-tab-pane label="收货单箱" name="second">

                  <el-row :gutter="24" class="mb8">
                    <el-col :span="12">
                      <el-table height="50vh" :data="receiveList_DetailList" ref="wmsReceiveDetail"
                        @row-click="rowClickDetail">
                        <!-- <el-table-column type="index" width="50" label="序号" align="center" /> -->
                        <el-table-column fixed="left" label="采购单号" prop="purchaseNo"> </el-table-column>
                        <el-table-column label="行号" prop="receiveLine"> </el-table-column>
                        <el-table-column label="物料编码" prop="materialCode"
                          :width="tableWidth(receiveList_DetailList.map(x => { x.materialCode }))">
                        </el-table-column>
                        <el-table-column label="物料名称" prop="materialName"
                          :width="tableWidth(receiveList_DetailList.map(x => { x.materialName }))">
                        </el-table-column>
                        <el-table-column label="单位" prop="materialUnit"> </el-table-column>
                        <el-table-column label="规格型号" prop="specification">
                        </el-table-column>
                        <el-table-column label="已收数量" prop="receiveQty"> </el-table-column>
                        <el-table-column label="数量-总数" prop="qty"> </el-table-column>
                      </el-table>
                    </el-col>

                    <el-col :span="12">
                      <!-- 收货单箱信息表 -->
                      <el-table height="50vh" :data="receiveList_BoxList" @selection-change="handleSelectionChange">
                        <!-- <el-table-column type="index" width="50" label="序号" align="center" /> -->
                        <!-- <el-table-column type="selection" width="55" align="center" /> -->
                        <el-table-column fixed="left" label="箱号" width="300" align="center" prop="boxNo" />
                        <el-table-column label="物料编码" align="center" prop="materialCode"
                          :width="tableWidth(receiveList_BoxList.map(x => { x.materialCode }))" />
                        <el-table-column label="物料名称" align="center" prop="materialName"
                          :width="tableWidth(receiveList_BoxList.map(x => { x.materialName }))" />
                        <el-table-column label="物料单位" align="center" prop="materialUnit" />
                        <el-table-column label="规格型号" align="center" prop="specification" />
                      </el-table>
                    </el-col>
                  </el-row>
                </el-tab-pane>

              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listReceive,
  getReceive,
  delReceive,
  addReceive,
  updateReceive,
} from "@/api/system/receive";
import {
  listReceiveDetail,
  getReceiveDetail,
} from "@/api/system/receiveDetail";
import { listReceiveBox, getReceiveBox } from "@/api/system/receiveBox";
export default {
  name: "Receive",
  data() {
    return {
        //详情
      activeNamesInfo: ["1", "2"],
      labelPosition: "left",
      activeName: "first",
      detialOpen: false,
      receiveLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsReceiveDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货单表格数据
      receiveList: [],
      // 弹出收货单明细表格数据
      wmsReceiveDetailList: [],
      // 收货详细列表加载数据
      detial_loading: false,
      //  收货单明细表格数据
      receiveList_DetailList: [],
      //  收货单箱信息表格数据
      receiveList_BoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      receiveDetailList: [],
      //
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick() {
      console.log(this.activeName);

    },
    detailClose() {
      this.detialOpen = false;
    },
    //点击详情的时候的方法
    clickDetails(row) {
      getReceive(row.id).then((response) => {
        console.log('dwdwadadwds', response);
        this.form = response.data;
        this.detialOpen = true;

        this.detail_list(row);
      });



    },
    /** 查询收货单列表 */
    getList() {
      this.loading = true;
      listReceive(this.queryParams).then((response) => {
        // this.receiveList = this.sortArrayByField(response.rows, 'createTime');
        this.receiveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询收货单单明细列表 */
    detail_list(row) {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        receiveId: row.id,
      };
      listReceiveDetail(queryParams).then((response) => {
        // this.receiveList_DetailList = this.sortArrayByField(response.rows, 'createTime');
        this.receiveList_DetailList = response.rows;


        this.detial_loading = false;
        this.receiveList_BoxList = [];
        // console.log("结果", response);
      });
    },
    /** 查询收货单箱信息列表 */
    detail_box(row) {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        receiveDetailId: row.id,
      };
      listReceiveBox(queryParams).then((response) => {
        // this.receiveList_BoxList = this.sortArrayByField(response.rows, 'createTime');
        this.receiveList_BoxList = response.rows;
        this.detial_loading = false;
        // console.log("结果", response);
      });
    },
    //某一行被点击的时候
    rowClick(row, column, event) {
      this.detial_loading = true;
      // console.log("被点击了", row);
      this.detail_list(row);
    },
    // 某一行详细订单被点击时候
    rowClickDetail(row, column, event) {
      this.detial_loading = true;
      // console.log("详细订单被点击了", row);
      this.detail_box(row);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      //  收货单明细表格数据

      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        receiveNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        receiveState: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        //  收货单明细表格数据
        receiveList_DetailList: null,
        //  收货单箱信息表格数据
        receiveList_BoxList: null,
      };
      this.wmsReceiveDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加收货单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getReceive(id).then((response) => {


        this.form = response.data;
        this.wmsReceiveDetailList = response.data.wmsReceiveDetailList;


        //  收货单箱信息表格数据
        this.receiveList_DetailList = [];
        this.open = true;
        this.title = "修改收货单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsReceiveDetailList = this.wmsReceiveDetailList;
          if (this.form.id != null) {
            updateReceive(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReceive(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除收货单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delReceive(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 收货单明细序号 */
    rowWmsReceiveDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 收货单明细添加按钮操作 */
    handleAddWmsReceiveDetail() {
      let obj = {};
      obj.receiveNo = "";
      obj.purchaseNo = "";
      obj.purchaseNo = "";
      obj.purchaseDetailId = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.receiveLine = "";
      obj.receiveQty = "";
      obj.batchNo = "";
      obj.remark = "";
      obj.comId = "";
      this.wmsReceiveDetailList.push(obj);
    },
    /** 收货单明细删除按钮操作 */
    handleDeleteWmsReceiveDetail() {
      if (this.checkedWmsReceiveDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的收货单明细数据");
      } else {
        const wmsReceiveDetailList = this.wmsReceiveDetailList;
        const checkedWmsReceiveDetail = this.checkedWmsReceiveDetail;
        this.wmsReceiveDetailList = wmsReceiveDetailList.filter(function (
          item
        ) {
          return checkedWmsReceiveDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsReceiveDetailSelectionChange(selection) {
      this.checkedWmsReceiveDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/receive/export",
        {
          ...this.queryParams,
        },
        `收货单_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
