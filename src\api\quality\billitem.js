import request from '@/utils/request'

// 查询iqc检检测项目列表
export function listBillitem(query) {
  return request({
    url: '/quality/billitem/list',
    method: 'get',
    params: query
  })
}


// 查询iqc检检测项目详细
export function getBillitem(id) {
  return request({
    url: '/quality/billitem/' + id,
    method: 'get'
  })
}

// 新增iqc检检测项目
export function addBillitem(data) {
  return request({
    url: '/quality/billitem',
    method: 'post',
    data: data
  })
}

// 修改iqc检检测项目
export function updateBillitem(data) {
  return request({
    url: '/quality/billitem',
    method: 'put',
    data: data
  })
}

// 删除iqc检检测项目
export function delBillitem(id) {
  return request({
    url: '/quality/billitem/' + id,
    method: 'delete'
  })
}
