import request from '@/utils/request'

// 查询销售退货列表
export function listWms_sales_return(query) {
  return request({
    url: '/system/wms_sales_return/list',
    method: 'get',
    params: query
  })
}

// 查询销售退货详细
export function getWms_sales_return(id) {
  return request({
    url: '/system/wms_sales_return/' + id,
    method: 'get'
  })
}

// 新增销售退货
export function addWms_sales_return(data) {
  return request({
    url: '/system/wms_sales_return',
    method: 'post',
    data: data
  })
}

// 修改销售退货
export function updateWms_sales_return(data) {
  return request({
    url: '/system/wms_sales_return',
    method: 'put',
    data: data
  })
}

// 删除销售退货
export function delWms_sales_return(id) {
  return request({
    url: '/system/wms_sales_return/' + id,
    method: 'delete'
  })
}
