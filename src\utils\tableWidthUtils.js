
export function tableWidth(columnData, options = {}) {
  const { minWidth = 139 } = options;
  if (!columnData || columnData == null) return minWidth;

  const maxLength = columnData.reduce((max, text) => {
    const len = String(text)
      .split('')
      .reduce((acc, char) => acc + (char.charCodeAt(0) > 255 ? 2 : 1), 0);
    return len > max ? len : max;
  }, 0);
  return maxLength * 8.8 + 80;
}