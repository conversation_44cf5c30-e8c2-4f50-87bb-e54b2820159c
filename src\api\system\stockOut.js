import request from '@/utils/request'

// 查询出库单主列表
export function listStockOut(query) {
  return request({
    url: '/system/stockOut/list',
    method: 'get',
    params: query
  })
}

// 查询出库单主详细
export function getStockOut(id) {
  return request({
    url: '/system/stockOut/' + id,
    method: 'get'
  })
}

// 新增出库单主
export function addStockOut(data) {
  return request({
    url: '/system/stockOut',
    method: 'post',
    data: data
  })
}

// 修改出库单主
export function updateStockOut(data) {
  return request({
    url: '/system/stockOut',
    method: 'put',
    data: data
  })
}

// 删除出库单主
export function delStockOut(id) {
  return request({
    url: '/system/stockOut/' + id,
    method: 'delete'
  })
}
