import request from '@/utils/request'

// 查询调拨标签列表
export function listAllotBox(query) {
  return request({
    url: '/system/allotBox/list',
    method: 'get',
    params: query
  })
}

// 查询调拨标签详细
export function getAllotBox(id) {
  return request({
    url: '/system/allotBox/' + id,
    method: 'get'
  })
}

// 新增调拨标签
export function addAllotBox(data) {
  return request({
    url: '/system/allotBox',
    method: 'post',
    data: data
  })
}

// 修改调拨标签
export function updateAllotBox(data) {
  return request({
    url: '/system/allotBox',
    method: 'put',
    data: data
  })
}

// 删除调拨标签
export function delAllotBox(id) {
  return request({
    url: '/system/allotBox/' + id,
    method: 'delete'
  })
}
