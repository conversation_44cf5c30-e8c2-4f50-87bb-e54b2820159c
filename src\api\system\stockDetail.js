import request from '@/utils/request'

// 查询盘点明细列表
export function listStockDetail(query) {
  return request({
    url: '/system/stockDetail/list',
    method: 'get',
    params: query
  })
}

// 查询盘点明细详细
export function getStockDetail(id) {
  return request({
    url: '/system/stockDetail/' + id,
    method: 'get'
  })
}

// 新增盘点明细
export function addStockDetail(data) {
  return request({
    url: '/system/stockDetail',
    method: 'post',
    data: data
  })
}

// 修改盘点明细
export function updateStockDetail(data) {
  return request({
    url: '/system/stockDetail',
    method: 'put',
    data: data
  })
}

// 删除盘点明细
export function delStockDetail(id) {
  return request({
    url: '/system/stockDetail/' + id,
    method: 'delete'
  })
}
