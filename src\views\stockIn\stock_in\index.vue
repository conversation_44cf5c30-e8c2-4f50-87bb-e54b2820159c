<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form class="fromInputClass" :model="queryParams" ref="queryForm" size="small" :inline="true"
        :label-position="labelPosition" label-width="85px" v-show="showSearch">
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input v-model="queryParams.stockInNo" placeholder="请输入入库单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input v-model="queryParams.purchaseNo" placeholder="请输入采购单号" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input v-model="queryParams.supplierCode" placeholder="请输入供应商编码" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="收货单号" prop="reviceNo">
          <el-input v-model="queryParams.reviceNo" placeholder="请输入收货单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="采购单号" prop="purchaseNo">
          <el-input v-model="queryParams.purchaseNo" placeholder="请输入采购单号" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <!-- <el-form-item label="质检状态" prop="qualityState">
          <el-select v-model="queryParams.qualityState" placeholder="请选择质检状态" clearable>
            <el-option v-for="dict in dict.type.quality_state" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="入库日期" prop="stockInDate">
          <el-date-picker clearable v-model="queryParams.stockInDate" type="date" value-format="yyyy-MM-dd"
            placeholder=" 选择入库日期">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item v-show="moreSelect" label="入库状态" prop="stockInStateArr">
          <el-select multiple v-model="queryParams.stockInStateArr" placeholder="请选择入库状态">
            <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>

        <div v-show="false">
          <el-form-item label="供应商id" prop="supplierId">
            <el-input v-model="queryParams.supplierId" placeholder="请输入供应商id" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="入库类型" prop="stockInType">
            <el-select v-model="queryParams.stockInType" placeholder="请选择入库类型" clearable>
              <el-option v-for="dict in dict.type.stock_in_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="质检状态" prop="qualityState">
            <el-input v-model="queryParams.qualityState" placeholder="请输入质检状态" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="仓库id" prop="warehouseId">
            <el-input v-model="queryParams.warehouseId" placeholder="请输入仓库id" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="仓库编码" prop="warehouseCode">
            <el-input v-model="queryParams.warehouseCode" placeholder="请输入仓库编码" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="仓库名称" prop="warehouseName">
            <el-input v-model="queryParams.warehouseName" placeholder="请输入仓库名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="物料id" prop="materialId">
            <el-input v-model="queryParams.materialId" placeholder="请输入物料id" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="物料名称" prop="materialName">
            <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="组织" prop="comId">
            <el-input v-model="queryParams.comId" placeholder="请输入组织" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
        </div>

        <el-form-item>
          <span @click="moreSelect = !moreSelect" class="moreClass">
            {{ moreSelect ? "收起" : "更多条件"
            }}<i :class="!moreSelect ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i></span>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8" v-show="false">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:stock_in:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:stock_in:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:stock_in:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:stock_in:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <!-- 展示的列表 -->
      <el-table v-loading="loading" :data="stock_inList" @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="入库单号" align="center" prop="stockInNo"
          :width="tableWidth(stock_inList.map((x) => x.stockInNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip placement="top" effect="dark" :content="scope.row.stockInNo">
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.stockInNo
                  }}</span>
              </el-tooltip>
              <i style="margin-left: 10px; cursor: pointer" class="el-icon-document-copy"
                v-clipboard:copy="scope.row.stockInNo" v-clipboard:success="onCopy"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="采购单号" align="center" prop="purchaseNo" width="180" />
        <el-table-column label="收货单号" align="center" prop="reviceNo" width="180" />
        <el-table-column label="供应商编码" align="center" prop="supplierCode" width="180" />
        <el-table-column label="供应商名称" align="center" prop="supplierName" width="180" />
        <el-table-column label="入库日期" align="center" prop="stockInDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.stockInDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="stockInState">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.stock_in_state" :value="scope.row.stockInState" />
          </template>
        </el-table-column>
        <el-table-column label="质检状态" align="center" prop="qualityState">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.quality_state" :value="scope.row.qualityState" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170" align="center" class-name="small-padding fixed-width" fixed="right">
          <template slot-scope="scope">
            <el-button v-show="closeCheck(scope.row)" size="mini" type="text" icon="el-icon-minus"
              @click="updateType(scope.row, 'CLOSED')" v-hasPermi="['system:stock_in:edit']">关闭</el-button>
            <el-button size="mini" type="text" icon="el-icon-s-order" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:stock_in:edit']">详情</el-button>
            <el-button v-show="distroyCheck(scope.row)" size="mini" type="text" icon="el-icon-delete-solid"
              @click="updateType(scope.row, 'HAVE_BEEN')" v-hasPermi="['system:stock_in:remove']">作废</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
        
      <el-drawer :title="title" :visible.sync="open" :size="'75%'" append-to-body>
        <el-form class="" ref="form" :model="form" :rules="rules" label-width="90px" size="small"
          :label-position="labelPosition" :inline="true">
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="采购入库单信息" name="1">
              <el-form-item label="入库单号" prop="stockInNo">
                <el-input disabled v-model="form.stockInNo" placeholder="请输入入库单号" />
              </el-form-item>
              <el-form-item label="采购单号" prop="purchaseNo">
                <el-input disabled v-model="form.purchaseNo" placeholder="请输入采购单号" />
              </el-form-item>
              <el-form-item label="供应商编码" prop="supplierCode">
                <el-input disabled v-model="form.supplierCode" placeholder="请输入供应商编码" />
              </el-form-item>
              <el-form-item label="供应商名称" prop="supplierName">
                <el-input disabled v-model="form.supplierName" placeholder="请输入供应商名称" />
              </el-form-item>
              <el-form-item label="入库日期" prop="stockInDate">
                <el-date-picker disabled clearable v-model="form.stockInDate" type="date" value-format="yyyy-MM-dd"
                  placeholder=" 选择入库日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="入库状态" prop="stockInState">
                <el-select disabled v-model="form.stockInState" placeholder="请选择入库状态">
                  <el-option v-for="dict in dict.type.stock_in_state" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="采购入库明细" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="入库明细" name="first">
                  <StockDetail :stock_in_id="stock_in_id" :key="refreshValue" :activeName="activeName" />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <StockDetail @sendDetailId="receiveDetailData" :stock_in_id="stock_in_id" :key="refreshValue"
                        :activeName="activeName" />
                    </div>
                    <div style="width: 48%">
                      <StockBox :stock_detail_id="stock_detail_id" :key="stock_detail_id" />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listStock_in,
  getStock_in,
  delStock_in,
  addStock_in,
  updateStock_in,
} from "@/api/system/stock_in";
import StockDetail from "../stock_detail/componet.vue";
import StockBox from "../stock_box/stockBoxComponet.vue";
// import { component } from "vue/types/umd";
export default {
  name: "Stock_in",
  dicts: ["stock_in_type", "stock_in_state", "quality_state"],
  components: {
    StockDetail,
    StockBox, // 注册子组件
  },
  data() {
    return {
      //详情
      activeNamesInfo: ["1", "2"],
      // 刷新子组件的key 在点击详情的时候变化
      refreshValue: -1,
      // 表单查询时候的展示更多条件的boolean
      moreSelect: false,
      //传给箱表的id 来源于detail组件
      stock_detail_id: "",
      // 主表具体的id
      stock_in_id: "",
      //tabs标签的name
      //tabs标签的name
      activeName: "first",
      //table对齐方式
      labelPosition: "right",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockInDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购入库表格数据
      stock_inList: [],
      // 入库单明细表格数据
      wmsStockInDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: null,
        stockInDate: null,
        stockInState: null,
        stockInStateArr: [],
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stockInNo: [
          { required: true, message: "入库单号不能为空", trigger: "blur" },
        ],
        stockInType: [
          { required: true, message: "入库类型不能为空", trigger: "change" },
        ],
        stockInState: [
          { required: true, message: "入库状态不能为空", trigger: "blur" },
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    //作废或者完成录入的时候的更新方法
    //作废或者完成录入的时候的更新方法
    updateType(row, type) {
      this.$modal
        .confirm('是否确认更改入库单号为"' + row.stockInNo + '"的数据项？')
        .then(function () { })
        .then(() => {
          getStock_in(row.id).then((response) => {
            this.form = response.data;
            this.form.stockInState = type;
            updateStock_in(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.getList();
            });
          });
        })
        .catch(() => { });
    },
    //判别销毁或者作废按钮应不应该出来的方法
    //判别销毁或者作废按钮应不应该出来的方法
    distroyCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "PUT_STORAGE":
          return false;
        case "BE_IN_STORAGE":
          return false;
        case "HAVE_BEEN":
          return false;
        default:
          return true;
      }
    },
    // 判断关闭按钮是否应该出来
    // 判断关闭按钮是否应该出来
    closeCheck(row) {
      const type = row.stockInState;
      switch (type) {
        case "CLOSED":
          return false;
        case "HAVE_BEEN":
          return false;
        case "RETURNED":
          return false;
        case "PUT_STORAGE":
          return false;
        case "BE_IN_STORAGE":
          return false;
        default:
          return true;
      }
    },
    // 子传父 方法处理
    // 子传父 方法处理
    receiveDetailData(data) {
      this.stock_detail_id = data;
    },
    // tabs标签变化的时候 清空box表的数据
    handleClick() {
      if (this.activeName == "first") {
        this.stock_detail_id = null;
      }
    },
    /** 查询采购入库列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: "createTime",
        isAsc: "desc",
      };
      listStock_in(params).then((response) => {
        this.stock_inList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        purchaseNo: null,
        purchaseNo: null,
        reviceNo: null,
        reviceId: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockInType: null,
        stockInDate: null,
        stockInState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockInOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsStockInDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购入库";
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      this.refreshValue += 1;
      this.reset();
      this.stock_in_id = row.id;
      const id = row.id || this.ids;
      getStock_in(id).then((response) => {
        this.form = response.data;
        this.wmsStockInDetailList = response.data.wmsStockInDetailList;
        this.open = true;
        this.title = "查看采购入库详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsStockInDetailList = this.wmsStockInDetailList;
          if (this.form.id != null) {
            updateStock_in(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStock_in(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除采购入库编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStock_in(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 入库单明细序号 */
    rowWmsStockInDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 入库单明细添加按钮操作 */
    handleAddWmsStockInDetail() {
      let obj = {};
      obj.purchaseDetailId = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.incomingQty = "";
      obj.stockInLine = "";
      obj.stockInState = "";
      obj.qualityState = "";
      obj.qualifiedQty = "";
      obj.batchNo = "";
      obj.remark = "";
      obj.comId = "";
      obj.stockInNo = "";
      this.wmsStockInDetailList.push(obj);
    },
    /** 入库单明细删除按钮操作 */
    handleDeleteWmsStockInDetail() {
      if (this.checkedWmsStockInDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单明细数据");
      } else {
        const wmsStockInDetailList = this.wmsStockInDetailList;
        const checkedWmsStockInDetail = this.checkedWmsStockInDetail;
        this.wmsStockInDetailList = wmsStockInDetailList.filter(function (
          item
        ) {
          return checkedWmsStockInDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsStockInDetailSelectionChange(selection) {
      this.checkedWmsStockInDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stock_in/export",
        {
          ...this.queryParams,
        },
        `采购入库_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-form {
  padding: 8px 0 !important;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.moreClass {
  cursor: pointer;
  color: rgb(24, 144, 255);
  font-size: 13px;
  margin-left: 1vw;
  margin-right: 7px;
}
</style>