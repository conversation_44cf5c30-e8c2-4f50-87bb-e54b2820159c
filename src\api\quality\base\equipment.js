import request from '@/utils/request'

// 查询检验设备列表
export function listEquipment(query) {
  return request({
    url: '/quality/equipment/list',
    method: 'get',
    params: query
  })
}

// 查询检验设备详细
export function getEquipment(id) {
  return request({
    url: '/quality/equipment/' + id,
    method: 'get'
  })
}

// 新增检验设备
export function addEquipment(data) {
  return request({
    url: '/quality/equipment',
    method: 'post',
    data: data
  })
}

// 修改检验设备
export function updateEquipment(data) {
  return request({
    url: '/quality/equipment',
    method: 'put',
    data: data
  })
}

// 删除检验设备
export function delEquipment(id) {
  return request({
    url: '/quality/equipment/' + id,
    method: 'delete'
  })
}
