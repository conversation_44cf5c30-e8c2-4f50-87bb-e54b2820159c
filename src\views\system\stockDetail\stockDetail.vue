<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <!-- <el-form-item label="盘点主表id" prop="checkId">
          <el-input
            v-model="queryParams.checkId"
            placeholder="请输入盘点主表id"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <!-- <el-form-item label="盘点单号" prop="checkNo">
          <el-input
            v-model="queryParams.checkNo"
            placeholder="请输入盘点单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库id" prop="warehouseId">
          <el-input
            v-model="queryParams.warehouseId"
            placeholder="请输入仓库id"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库编码" prop="warehouseCode">
          <el-input
            v-model="queryParams.warehouseCode"
            placeholder="请输入仓库编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="仓库名称" prop="warehouseName">
          <el-input
            v-model="queryParams.warehouseName"
            placeholder="请输入仓库名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料id" prop="materialId">
          <el-input
            v-model="queryParams.materialId"
            placeholder="请输入物料id"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="盘点状态；字典inventory_status"
          prop="inventoryStatus"
        >
          <el-select
            v-model="queryParams.inventoryStatus"
            placeholder="请选择盘点状态；字典inventory_status"
            clearable
          >
            <el-option
              v-for="dict in dict.type.inventory_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预盘量" prop="expectedQty">
          <el-input
            v-model="queryParams.expectedQty"
            placeholder="请输入预盘量"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="实盘量" prop="actualQty">
          <el-input
            v-model="queryParams.actualQty"
            placeholder="请输入实盘量"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="盘赢" prop="differentQty">
          <el-input
            v-model="queryParams.differentQty"
            placeholder="请输入盘赢"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库区id" prop="areaId">
          <el-input
            v-model="queryParams.areaId"
            placeholder="请输入库区id"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库区编码" prop="areaCode">
          <el-input
            v-model="queryParams.areaCode"
            placeholder="请输入库区编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库区名称" prop="areaName">
          <el-input
            v-model="queryParams.areaName"
            placeholder="请输入库区名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库位id" prop="locationId">
          <el-input
            v-model="queryParams.locationId"
            placeholder="请输入库位id"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库位编码" prop="locationCode">
          <el-input
            v-model="queryParams.locationCode"
            placeholder="请输入库位编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="库位名称" prop="locationName">
          <el-input
            v-model="queryParams.locationName"
            placeholder="请输入库位名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="组织" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入组织"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <!-- <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item> -->
      </el-form>
      <el-table
        height="62vh"
        v-loading="loading"
        :data="stockDetailList"
        @selection-change="handleSelectionChange"
      >
    
        <el-table-column label="盘点单号" align="center" prop="checkNo" />
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" />
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          label="盘点状态"
          align="center"
          prop="inventoryStatus"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.inventory_status"
              :value="scope.row.inventoryStatus"
            />
          </template>
        </el-table-column>
        <el-table-column label="预盘量" align="center" prop="expectedQty" />
        <el-table-column label="实盘量" align="center" prop="actualQty" />
        <el-table-column label="盘赢" align="center" prop="differentQty" />
        <el-table-column label="库区编码" align="center" prop="areaCode" />
        <el-table-column label="库区名称" align="center" prop="areaName" />
        <el-table-column label="库位编码" align="center" prop="locationCode" />
        <el-table-column label="库位名称" align="center" prop="locationName" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </div>
  </div>
</template>

<script>
import {
  listStockDetail,
  getStockDetail,
  delStockDetail,
  addStockDetail,
  updateStockDetail,
} from "@/api/system/stockDetail";

export default {
  name: "StockDetail",
  dicts: ["inventory_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 盘点明细表格数据
      stockDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkId: null,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null,
        expectedQty: null,
        actualQty: null,
        differentQty: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询盘点明细列表 */
    getList() {
      this.loading = true;
      listStockDetail(this.queryParams).then((response) => {
        this.stockDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        checkId: null,
        checkNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        inventoryStatus: null,
        expectedQty: null,
        actualQty: null,
        differentQty: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加盘点明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStockDetail(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改盘点明细";
      });
    },
    sendDetailId(row, colom, env) {
      this.$emit("sendDetailId", row.id);
      console.log("row");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateStockDetail(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStockDetail(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除盘点明细编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStockDetail(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stockDetail/export",
        {
          ...this.queryParams,
        },
        `盘点明细_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
