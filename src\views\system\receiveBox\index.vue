<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="收货主表id" prop="receiveId">
        <el-input
          v-model="queryParams.receiveId"
          placeholder="请输入收货主表id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货单号" prop="receiveNo">
        <el-input
          v-model="queryParams.receiveNo"
          placeholder="请输入收货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收货明细id" prop="receiveDetailId">
        <el-input
          v-model="queryParams.receiveDetailId"
          placeholder="请输入收货明细id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:receiveBox:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:receiveBox:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:receiveBox:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:receiveBox:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="receiveBoxList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="收货主表id" align="center" prop="receiveId" />
      <el-table-column label="收货单号" align="center" prop="receiveNo" />
      <el-table-column label="收货明细id" align="center" prop="receiveDetailId" />
      <el-table-column label="物料id" align="center" prop="materialId" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="规格型号" align="center" prop="specification" />
      <el-table-column label="单位" align="center" prop="materialUnit" />
      <el-table-column label="数量" align="center" prop="qty" />
      <el-table-column label="箱号" align="center" prop="boxNo" />
      <el-table-column label="箱二维码" align="center" prop="qrCode" />
      <el-table-column label="批次" align="center" prop="batchNo" />
      <el-table-column label="是否暂存；" align="center" prop="isStaging" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="生产日期" align="center" prop="dateCode" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCode, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保质期" align="center" prop="expirationDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:receiveBox:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:receiveBox:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收货单箱信息对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="收货主表id" prop="receiveId" style="width: 240px;">
          <el-input v-model="form.receiveId" placeholder="请输入收货主表id" />
        </el-form-item>
        <el-form-item label="收货单号" prop="receiveNo" style="width: 240px;">
          <el-input v-model="form.receiveNo" placeholder="请输入收货单号" />
        </el-form-item>
        <el-form-item label="收货明细id" prop="receiveDetailId" style="width: 240px;">
          <el-input v-model="form.receiveDetailId" placeholder="请输入收货明细id" />
        </el-form-item>
        <el-form-item label="物料id" prop="materialId" style="width: 240px;">
          <el-input v-model="form.materialId" placeholder="请输入物料id" />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode" style="width: 240px;">
          <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName" style="width: 240px;">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification" style="width: 240px;">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="单位" prop="materialUnit" style="width: 240px;">
          <el-input v-model="form.materialUnit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="数量" prop="qty" style="width: 240px;">
          <el-input v-model="form.qty" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="箱号" prop="boxNo" style="width: 240px;">
          <el-input v-model="form.boxNo" placeholder="请输入箱号" />
        </el-form-item>
        <el-form-item label="箱二维码" prop="qrCode" style="width: 700px;">
          <el-input v-model="form.qrCode" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="批次" prop="batchNo" style="width: 240px;">
          <el-input v-model="form.batchNo" placeholder="请输入批次" />
        </el-form-item>
        <el-form-item label="是否暂存；" prop="isStaging" style="width: 240px;">
          <el-input v-model="form.isStaging" placeholder="请输入是否暂存；" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="生产日期" prop="dateCode" style="width: 240px;">
          <el-date-picker clearable
            v-model="form.dateCode"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生产日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保质期" prop="expirationDate" style="width: 240px;">
          <el-date-picker clearable
            v-model="form.expirationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保质期">
          </el-date-picker>
        </el-form-item>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listReceiveBox, getReceiveBox, delReceiveBox, addReceiveBox, updateReceiveBox } from "@/api/system/receiveBox";

export default {
  name: "ReceiveBox",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货单箱信息表格数据
      receiveBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        receiveId: null,
        receiveNo: null,
        receiveDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询收货单箱信息列表 */
    getList() {
      this.loading = true;
      listReceiveBox(this.queryParams).then(response => {
        this.receiveBoxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        receiveId: null,
        receiveNo: null,
        receiveDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        batchNo: null,
        isStaging: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        dateCode: null,
        expirationDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加收货单箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getReceiveBox(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改收货单箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateReceiveBox(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReceiveBox(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除收货单箱信息编号为"' + ids + '"的数据项？').then(function() {
        return delReceiveBox(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/receiveBox/export', {
        ...this.queryParams
      }, `收货单箱信息_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
