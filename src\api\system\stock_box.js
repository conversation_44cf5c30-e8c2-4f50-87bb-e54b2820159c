import request from '@/utils/request'

// 查询入库单箱信息列表
export function listStock_box(query) {
  return request({
    url: '/system/stock_box/list',
    method: 'get',
    params: query
  })
}

// 查询入库单箱信息详细
export function getStock_box(id) {
  return request({
    url: '/system/stock_box/' + id,
    method: 'get'
  })
}

// 新增入库单箱信息
export function addStock_box(data) {
  return request({
    url: '/system/stock_box',
    method: 'post',
    data: data
  })
}

// 修改入库单箱信息
export function updateStock_box(data) {
  return request({
    url: '/system/stock_box',
    method: 'put',
    data: data
  })
}

// 删除入库单箱信息
export function delStock_box(id) {
  return request({
    url: '/system/stock_box/' + id,
    method: 'delete'
  })
}
