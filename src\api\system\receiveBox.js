import request from '@/utils/request'

// 查询收货单箱信息列表
export function listReceiveBox(query) {
  return request({
    url: '/system/receiveBox/list',
    method: 'get',
    params: query
  })
}

// 查询收货单箱信息详细
export function getReceiveBox(id) {
  return request({
    url: '/system/receiveBox/' + id,
    method: 'get'
  })
}

// 新增收货单箱信息
export function addReceiveBox(data) {
  return request({
    url: '/system/receiveBox',
    method: 'post',
    data: data
  })
}

// 修改收货单箱信息
export function updateReceiveBox(data) {
  return request({
    url: '/system/receiveBox',
    method: 'put',
    data: data
  })
}

// 删除收货单箱信息
export function delReceiveBox(id) {
  return request({
    url: '/system/receiveBox/' + id,
    method: 'delete'
  })
}
