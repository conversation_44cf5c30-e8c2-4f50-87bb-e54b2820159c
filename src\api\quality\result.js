import request from '@/utils/request'

// 查询iqc检验结果列表
export function listResult(query) {
  return request({
    url: '/quality/result/list',
    method: 'get',
    params: query
  })
}

// 根据物料查询检验项目
export function listByPart(query) {
  return request({
    url: '/quality/result/listByPart',
    method: 'get',
    params: query
  })
}

// 查询iqc检验结果详细
export function getResult(id) {
  return request({
    url: '/quality/result/' + id,
    method: 'get'
  })
}

// 新增iqc检验结果
export function addResult(data) {
  return request({
    url: '/quality/result',
    method: 'post',
    data: data
  })
}

// 修改iqc检验结果
export function updateResult(data) {
  return request({
    url: '/quality/result',
    method: 'put',
    data: data
  })
}

// 删除iqc检验结果
export function delResult(id) {
  return request({
    url: '/quality/result/' + id,
    method: 'delete'
  })
}
