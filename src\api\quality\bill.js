import request from '@/utils/request'

// 查询iqc质检单据列表
export function listBill(query) {
  return request({
    url: '/quality/bill/list',
    method: 'get',
    params: query
  })
}

// 查询iqc质检单据详细
export function getBill(id) {
  return request({
    url: '/quality/bill/' + id,
    method: 'get'
  })
}

// 新增iqc质检单据
export function addBill(data) {
  return request({
    url: '/quality/bill',
    method: 'post',
    data: data
  })
}

// 进行iqc质检
export function addQuality(data) {
  return request({
    url: '/quality/bill/addQuality',
    method: 'post',
    data: data
  })
}

// 修改iqc质检单据
export function updateBill(data) {
  return request({
    url: '/quality/bill',
    method: 'put',
    data: data
  })
}

// 删除iqc质检单据
export function delBill(id) {
  return request({
    url: '/quality/bill/' + id,
    method: 'delete'
  })
}
