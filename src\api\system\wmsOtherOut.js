import request from '@/utils/request'

// 查询其他出库列表
export function listWmsOtherOut(query) {
  return request({
    url: '/system/wmsOtherOut/list',
    method: 'get',
    params: query
  })
}

// 查询其他出库详细
export function getWmsOtherOut(id) {
  return request({
    url: '/system/wmsOtherOut/' + id,
    method: 'get'
  })
}

// 新增其他出库
export function addWmsOtherOut(data) {
  return request({
    url: '/system/wmsOtherOut',
    method: 'post',
    data: data
  })
}

// 修改其他出库
export function updateWmsOtherOut(data) {
  return request({
    url: '/system/wmsOtherOut',
    method: 'put',
    data: data
  })
}

// 删除其他出库
export function delWmsOtherOut(id) {
  return request({
    url: '/system/wmsOtherOut/' + id,
    method: 'delete'
  })
}
