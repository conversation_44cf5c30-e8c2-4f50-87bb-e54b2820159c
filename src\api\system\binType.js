import request from '@/utils/request'

// 查询库位类型列表
export function listBinType(query) {
  return request({
    url: '/system/binType/list',
    method: 'get',
    params: query
  })
}

// 查询库位类型详细
export function getBinType(id) {
  return request({
    url: '/system/binType/' + id,
    method: 'get'
  })
}

// 新增库位类型
export function addBinType(data) {
  return request({
    url: '/system/binType',
    method: 'post',
    data: data
  })
}

// 修改库位类型
export function updateBinType(data) {
  return request({
    url: '/system/binType',
    method: 'put',
    data: data
  })
}

// 删除库位类型
export function delBinType(id) {
  return request({
    url: '/system/binType/' + id,
    method: 'delete'
  })
}
