<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="质检单号" prop="oqcNo">
          <el-input
            v-model="queryParams.iqcNo"
            placeholder="请输入质检单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="出库单号" prop="stockOutNo">
          <el-input
            v-model="queryParams.stockInNo"
            placeholder="请输入出库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="收货单号" prop="receiveNo">
          <el-input
            v-model="queryParams.receiveNo"
            placeholder="请输入收货单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input
            v-model="queryParams.supplierCode"
            placeholder="请输入供应商编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="queryParams.supplierName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="批次" prop="batchNo">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批次"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label="	
质检结果"
          prop="inspResult"
        >
          <el-select
            v-model="queryParams.inspResult"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.insp_result"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="质检状态" prop="billStatus">
          <el-select
            v-model="queryParams.billStatus"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.bill_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采购单号" prop="purchaseNo">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <!-- <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:bill:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['quality:bill:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['quality:bill:remove']"
            >删除</el-button
          >
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['quality:bill:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="billList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="index" width="50" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="质检单号"
          align="center"
          prop="oqcNo"
          :width="tableWidth(billList.map((x) => x.iqcNo))"
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.oqcNo"
              >
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.oqcNo
                }}</span>
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.iqcNo"
                v-clipboard:success="onCopy"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="出库单号"
          align="center"
          prop="stockOutNo"
          width="155"
        />
        <el-table-column
          label="收货单号"
          align="center"
          prop="receiveNo"
          width="150"
        />
        <el-table-column
          label="采购单号"
          align="center"
          prop="purchaseNo"
          width="150"
        />
        <el-table-column
          label="供应商编码"
          align="center"
          prop="supplierCode"
          width="120"
        />
        <el-table-column
          label="供应商名称"
          align="center"
          prop="supplierName"
          width="120"
        />
        <el-table-column
          label="物料编码"
          align="center"
          prop="materialCode"
          width="120"
        />
        <el-table-column
          label="物料名称"
          align="center"
          prop="materialName"
          width="120"
        />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="批次" align="center" prop="batchNo" />
        <el-table-column label="严格度" align="center" prop="adjustedSeverity">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.iqc_insp_config_strictness"
              :value="scope.row.adjustedSeverity"
            />
          </template>
        </el-table-column>
        <el-table-column label="质检结果" align="center" prop="inspResult">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.insp_result"
              :value="scope.row.inspResult"
            />
          </template>
        </el-table-column>
        <el-table-column label="质检状态" align="center" prop="billStatus">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.bill_status"
              :value="scope.row.billStatus"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="是否已免检；"
          align="center"
          prop="isExemption"
        /> -->
        <el-table-column label="检验维度" align="center" prop="inspectionFrom">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.inspection_from"
              :value="scope.row.inspectionFrom"
            />
          </template>
        </el-table-column>
        <el-table-column label="报检类型" align="center" prop="inspType">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.insp_type"
              :value="scope.row.inspType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="生产日期"
          align="center"
          prop="dateCode"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="保质期"
          align="center"
          prop="expirationDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.expirationDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="检验日期"
          align="center"
          prop="inspectionDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.inspectionDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-monitor"
              @click="handleQuality(scope.row)"
              v-hasPermi="['quality:bill:edit']"
              v-if="scope.row.billStatus == 'REVIEW'"
              >审核</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleSelect(scope.row)"
              >查看</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleSelect(scope.row)"
              v-hasPermi="['quality:bill:edit']"
              v-if="
                scope.row.billStatus == 'COMPLETED' &&
                scope.row.inspResult == 'unqualified'
              "
              >评审反馈</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  listBill,
  getBill,
  delBill,
  addBill,
  updateBill,
} from "@/api/quality/bill";

export default {
  name: "BillRespect",
  dicts: [
    "bill_status",
    "iqc_insp_config_strictness",
    "insp_result",
    "inspection_from",
    "inspection_from",
    "insp_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // iqc质检单据表格数据
      billList: [],
      // 质检单号
      iqcNos: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: "REVIEW", //待审核
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    /** 查询iqc质检单据列表 */
    getList() {
      this.loading = true;
      listBill(this.queryParams).then((response) => {
        this.billList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //查询
    handleSelect(row) {
      row.title = "IQC检验详情";
      row.status = "select";
      this.$router.push({ path: "/billCheck/index", query: row });
    },
    handleQuality(row) {
      //跳转审核页面
      row.title = "IQC审批";
      row.status = "update";
      this.$router.push({ path: "/billCheck/index", query: row });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        iqcNo: null,
        stockInId: null,
        stockInNo: null,
        stockInDetailId: null,
        receiveNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        qty: null,
        batchNo: null,
        adjustedSeverity: null,
        inspResult: null,
        billStatus: null,
        isExemption: null,
        inspectionFrom: null,
        inspType: null,
        dateCode: null,
        expirationDate: null,
        inspectionDate: null,
        purchaseNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.iqcNos = selection.map((item) => item.iqcNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加iqc质检单据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBill(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改iqc质检单据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBill(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBill(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const iqcNos = row.iqcNo || this.iqcNos;
      console.log("iqcNos", row.iqcNo);
      this.$modal
        .confirm('是否确认删除iqc质检单据编号为"' + iqcNos + '"的数据项？')
        .then(function () {
          return delBill(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/bill/export",
        {
          ...this.queryParams,
        },
        `iqc质检单据_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
