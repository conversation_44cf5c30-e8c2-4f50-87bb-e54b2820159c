<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="客户编码" prop="customCode">
          <el-input
            v-model="queryParams.customCode"
            placeholder="请输入客户编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户名称" prop="customName">
          <el-input
            v-model="queryParams.customName"
            placeholder="请输入客户名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:custom:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:custom:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:custom:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:custom:export']"
            >导出</el-button
          >
        </el-col>
        <!--  导入按钮-->
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleImport"
            >导入</el-button
          >
        </el-col>
        <!-- <file-upload
          v-model="fileList"
          :limit="3"
          :file-size="5"
          :file-type="['xls', 'xlsx']"
          is-show-tip
        /> -->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="customList"
        @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="id" align="center" prop="id" /> -->
        <el-table-column label="客户编码" align="center" prop="customCode" />
        <el-table-column label="客户名称" align="center" prop="customName" />
        <!-- <el-table-column label="客户类型" align="center" prop="customType" /> -->
        <el-table-column label="联系人" align="center" prop="contacts" />
        <el-table-column label="手机号" align="center" prop="phone" />
        <!-- <el-table-column label="状态" align="center" prop="status" /> -->
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:custom:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:custom:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改客户管理对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="客户信息" name="1">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="客户编码" prop="customCode">
                    <el-input
                      v-model="form.customCode"
                      placeholder="请输入客户编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户名称" prop="customName">
                    <el-input
                      v-model="form.customName"
                      placeholder="请输入客户名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系人" prop="contacts">
                    <el-input
                      v-model="form.contacts"
                      placeholder="请输入联系人"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>

          <!-- <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 数据导入对话框 -->
      <!-- <el-dialog
        title="数据导入"
        :visible.sync="excelOpen"
        width="400px"
        append-to-body
      >
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :action="uploadExcelUrl"
          :headers="headers"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :limit="1"
          :auto-upload="false"
        >
          <template #trigger>
            <el-button type="primary">上传文件</el-button>
          </template>

          <el-button class="ml-3" type="success" @click="submitUpload">
            上传
          </el-button>

          <template #tip>
            <div class="el-upload__tip">
              上传文件仅支持，xls/xlsx格式，文件大小不得超过1M
            </div>
          </template>
        </el-upload>
      </el-dialog> -->
      <el-dialog
        :title="upload.title"
        :visible.sync="upload.open"
        width="400px"
        append-to-body
        v-loading="loading"
      >
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :http-request="uploadFile"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <!-- <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
            </div> -->
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-debounce:3000="debounceClick"
            type="primary"
            @click="submitFileForm"
            >提 交</el-button
          >
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listCustom,
  getCustom,
  delCustom,
  addCustom,
  updateCustom,
} from "@/api/system/custom";
import { getToken } from "@/utils/auth";
import axios from "axios";
export default {
  name: "Custom",
  // props: {
  //   // 对应 Vue 3 中的 modelValue，Vue 2 中默认使用 value 作为双向绑定的 prop
  //   value: {
  //     type: [String, Object, Array],
  //     default: null,
  //   },
  //   // 大小限制(MB)
  //   fileSize: {
  //     type: Number,
  //     default: 1,
  //   },
  //   // 文件类型, 例如["xls", "xlsx"]
  //   fileType: {
  //     type: Array,
  //     default: () => ["xls", "xlsx"],
  //   },
  // },
  data() {
    return {
      // fileList: [], // 格式示例: [{ name: 'file1.jpg', url: 'https://example.com/file1.jpg' }]
      // headers: {
      //   Authorization: "",
      // },
      // uploadExcelUrl: process.env.VUE_APP_BASE_API + "/base/custom/import",
      // // 控制导入弹窗显隐
      // excelOpen: false,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/custom/importData",
      },

      activeNames: ["1"],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客户管理表格数据
      customList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customCode: null,
        customName: null,
        customType: null,
        contacts: null,
        phone: null,
        status: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        customCode: [
          { required: true, trigger: "blur", message: "客户编码不能为空" },
        ],
        customName: [
          { required: true, trigger: "blur", message: "客户名称不能为空" },
        ],
        contacts: [
          { required: true, trigger: "blur", message: "联系人不能为空" },
        ],
        phone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    // 挂载前去获取更新token
    // this.updateHeaders();
  },
  methods: {
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料类型导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      const now = new Date();
      const year = now.getFullYear(); // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 获取月份，月份从0开始所以要+1
      const date = String(now.getDate()).padStart(2, "0"); // 获取日期
      const hours = String(now.getHours()).padStart(2, "0"); // 获取小时
      const minutes = String(now.getMinutes()).padStart(2, "0"); // 获取分钟
      const seconds = String(now.getSeconds()).padStart(2, "0"); // 获取秒钟
      const simpleFormat = `${year}${month}${date}${hours}${minutes}${seconds}`;
      this.download(
        "system/custom/importTemplate",
        {},
        `客户${simpleFormat}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    //参数必须是param，才能获取到内容
    uploadFile(param) {
      // 获取上传的文件名
      var file = param.file;
      //发送请求的参数格式为FormData
      const formData = new FormData();
      formData.append("file", file);
      // 调用param中的钩子函数处理各种情况，这样就可以用在组件中用钩子了。也可以用res.code==200来进行判断处理各种情况
      this.startUploadFile(formData, param)
        .then((res) => {
          param.onSuccess(res);
        })
        .catch((err) => {
          param.onError(err);
        });
    },

    // 上传的请求（这里是request封装的请求，封装了请求头等信息） responseType: 'blob' 这个是关键
    //request是封装的请求方法
    startUploadFile(query, param) {
      return axios({
        url: this.upload.url,
        responseType: "blob",
        method: "post",
        data: query,
        headers: {
          Authorization: "Bearer " + getToken(),
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
    },
    // 文件上传成功处理、错误
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      console.log(response.data.size, new Blob([response]));
      if (response.data.size != 0) {
        const blob = new Blob([response]);
        let url = window.URL.createObjectURL(
          new Blob([response.data], { type: ".xlsx" })
        );
        let a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.setAttribute("download", `物料类型导入失败.xlsx`);
        document.body.appendChild(a);
        a.click();
        url = window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        if (!response) {
          return;
        }
        const name = decodeURI(response);
      }
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      // this.starLoading = true;
      this.loadingInstance = this.starLoading(); //调用
      // this.$refs.upload.submit();
      // console.log(this.$refs.upload.submit());

      this.$refs.upload.submit();
      console.log(this.upload.open, "this.upload.open == false");

      if (this.upload.open == true) {
        this.upload.open = false;
        this.loadingInstance.closeLoading();
        this.getList();
      }
    },
    starLoading() {
      // 创建一个 loading 实例并赋值给 loading 变量
      let loading = this.$loading({
        text: "加载中", // 设置 loading 文本为 "加载中"
        spinner: "el-icon-loading", // 使用 Element UI 提供的加载图标
        background: "rgba(0, 0, 0, 0.7)", // 设置 loading 遮罩层背景色为半透明黑色
        target: document.querySelector("body"), // 将 loading 遮罩层挂载到页面 body 元素上
      });
      // 返回一个包含关闭 loading 遮罩层方法的对象
      return {
        // 方法用于关闭 loading 遮罩层
        closeLoading: () => {
          loading.close(); // 调用 loading 实例的 close 方法关闭遮罩层
        },
      };
    },

    debounceClick(v) {
      console.log("触发一下，间隔多少毫秒：", v);
    },

    /** 查询客户管理列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: "createTime",
        isAsc: "desc",
      };
      listCustom(this.queryParams).then((response) => {
        this.customList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        customCode: null,
        customName: null,
        customType: null,
        contacts: null,
        phone: null,
        status: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getCustom(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改客户管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateCustom(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCustom(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitUpload() {
      // 使用 this.$refs 访问 ref 绑定的组件实例
      this.$refs.uploadRef.submit();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除客户管理编号为"' + row.customCode + '"的数据项？')
        .then(function () {
          return delCustom(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/custom/export",
        {
          ...this.queryParams,
        },
        `custom_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
