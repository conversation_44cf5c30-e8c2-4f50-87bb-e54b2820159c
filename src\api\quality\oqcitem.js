import request from '@/utils/request'

// 查询oqc检检测项目列表
export function listOqcitem(query) {
  return request({
    url: '/quality/oqcitem/list',
    method: 'get',
    params: query
  })
}

// 查询oqc检检测项目详细
export function getOqcitem(id) {
  return request({
    url: '/quality/oqcitem/' + id,
    method: 'get'
  })
}

// 新增oqc检检测项目
export function addOqcitem(data) {
  return request({
    url: '/quality/oqcitem',
    method: 'post',
    data: data
  })
}

// 修改oqc检检测项目
export function updateOqcitem(data) {
  return request({
    url: '/quality/oqcitem',
    method: 'put',
    data: data
  })
}

// 删除oqc检检测项目
export function delOqcitem(id) {
  return request({
    url: '/quality/oqcitem/' + id,
    method: 'delete'
  })
}
