import request from '@/utils/request'

// 查询数据库-库列表
export function listDbtable(query) {
  return request({
    url: '/system/dbtable/list',
    method: 'get',
    params: query
  })
}

// 查询数据库-库详细
export function getDbtable(id) {
  return request({
    url: '/system/dbtable/' + id,
    method: 'get'
  })
}

// 新增数据库-库
export function addDbtable(data) {
  return request({
    url: '/system/dbtable',
    method: 'post',
    data: data
  })
}

// 修改数据库-库
export function updateDbtable(data) {
  return request({
    url: '/system/dbtable',
    method: 'put',
    data: data
  })
}

// 删除数据库-库
export function delDbtable(id) {
  return request({
    url: '/system/dbtable/' + id,
    method: 'delete'
  })
}
