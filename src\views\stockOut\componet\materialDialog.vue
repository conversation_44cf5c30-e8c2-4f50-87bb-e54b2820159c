<template>
    <el-dialog title="添加出库明细" :visible.sync="materialDialogVisible" width="50%" :before-close="materialHandleClose">
        <el-form :data="materialList" :inline="true" label-width="100px">
            <el-table ref="materialListTable" :data="materialList" border stripe style="width: 100%"
                @selection-change="selectionMaterial">
                <el-table-column type="selection" align="center" />
                <el-table-column label="物料编码" align="center" prop="materialCode" />
                <el-table-column label="物料名称" align="center" prop="materialName" />
                <el-table-column label="规格型号" align="center" prop="specification" />
                <el-table-column label="单位" align="center" prop="materialUnit" />
            </el-table>
        </el-form>
        <pagination v-show="materialTotal > 0" :total="materialTotal" :page.sync="materialQueryParams.pageNum"
            :limit.sync="materialQueryParams.pageSize" @pagination="loadListMaterials" />
        <span slot="footer" class="dialog-footer">
            <el-button @click="materialHandleClose">取 消</el-button>
            <el-button type="primary" @click="materialHandleAdd">导 入</el-button>
        </span>
    </el-dialog>
</template>
<script>
import { listMaterial } from "@/api/system/material";
export default {
    name: 'materialDialog',
    props: {
        materialDialogVisible: Boolean
    },
    data() {
        return {
            // 物料表格数据
            materialList: [],
            // 选中的物料数据
            detailmaterialList: [],
            //物料总数量
            materialTotal: [],
            materialQueryParams: {
                pageNum: 1,
                pageSize: 10
            },
        };
    },
    created() {
        this.loadListMaterials();
    },
    methods: {
        // 获取物料列表的方法
        async loadListMaterials() {
            try {
                const { rows, total } = await listMaterial(this.materialQueryParams);
                this.materialList = rows;
                this.materialTotal = total;
            } catch (error) {
                console.error('获取物料列表失败', error);
                // 这里可以添加统一的错误提示
                this.$message.error('获取物料列表失败，请稍后重试');
            }
        },
        // 新增明细 勾选框发生勾选的时候的回调
        selectionMaterial(selection) {
            this.detailmaterialList = selection.map(item => ({
                ...item,
                materialId: item.id // 提前设置materialId，避免后续重复处理
            }));
            this.single = selection.length !== 1;
            this.multiple = selection.length === 0;
        },
        // 关闭物料选择弹窗
        materialHandleClose() {
            this.resetMaterialSelection();
            this.$emit('updateDialogVisible', this.materialDialogVisible)
        },
        // 取消勾选并清空选中列表
        resetMaterialSelection() {
            this.detailmaterialList = [];
            this.$refs?.materialListTable?.clearSelection();
        },
        // 确认添加物料到明细
        materialHandleAdd() {
            if (this.detailmaterialList.length === 0) {
                this.$message.warning('请选择要添加的物料');
                return;
            }
            // 使用展开运算符避免引用
            this.$emit('selectMateriaList', this.detailmaterialList)
            this.materialHandleClose();
        },
    }
}
</script>