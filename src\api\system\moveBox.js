import request from '@/utils/request'

// 查询移库箱信息列表
export function listMoveBox(query) {
  return request({
    url: '/system/moveBox/list',
    method: 'get',
    params: query
  })
}

// 查询移库箱信息详细
export function getMoveBox(id) {
  return request({
    url: '/system/moveBox/' + id,
    method: 'get'
  })
}

// 新增移库箱信息
export function addMoveBox(data) {
  return request({
    url: '/system/moveBox',
    method: 'post',
    data: data
  })
}

// 修改移库箱信息
export function updateMoveBox(data) {
  return request({
    url: '/system/moveBox',
    method: 'put',
    data: data
  })
}

// 删除移库箱信息
export function delMoveBox(id) {
  return request({
    url: '/system/moveBox/' + id,
    method: 'delete'
  })
}
