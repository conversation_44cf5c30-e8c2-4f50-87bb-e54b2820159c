import request from '@/utils/request'

// 查询库列表
export function listDbdatabase(query) {
  return request({
    url: '/system/dbdatabase/list',
    method: 'get',
    params: query
  })
}
//查看数据库表树列表
export function listDbdatabaseTree(query) {
  return request({
    url: '/system/dbdatabase/deptTree',
    method: 'get',
    params: query
  })
}

// 查询库详细
export function getDbdatabase(id) {
  return request({
    url: '/system/dbdatabase/' + id,
    method: 'get'
  })
}

// 新增库
export function addDbdatabase(data) {
  return request({
    url: '/system/dbdatabase',
    method: 'post',
    data: data
  })
}

// 修改库
export function updateDbdatabase(data) {
  return request({
    url: '/system/dbdatabase',
    method: 'put',
    data: data
  })
}

// 删除库
export function delDbdatabase(id) {
  return request({
    url: '/system/dbdatabase/' + id,
    method: 'delete'
  })
}
