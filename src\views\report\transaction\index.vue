<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="入库单号" prop="stockInNo">
          <el-input v-model="queryParams.stockInNo" placeholder="请输入入库单号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料编码/名称" prop="searchMaterialKeyword">
          <el-input v-model="queryParams.searchMaterialKeyword" placeholder="请输入物料编码或名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="仓库编码/名称" prop="searchWarehouseKeyword">
          <el-input v-model="queryParams.searchWarehouseKeyword" placeholder="请输入仓库编码或名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="库区编码/名称" prop="searchAreaKeyword">
          <el-input v-model="queryParams.searchAreaKeyword" placeholder="请输入库区编码或名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="库位编码/名称" prop="searchLocationKeyword">
          <el-input v-model="queryParams.searchLocationKeyword" placeholder="请输入库位编码或名称" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>

        <el-form-item label="箱号" prop="boxNo">
          <el-input v-model="queryParams.boxNo" placeholder="请输入箱号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="批次" prop="batchNo">
          <el-input v-model="queryParams.batchNo" placeholder="请输入批次" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="生产日期" prop="dateCode">
          <el-date-picker
            clearable
            v-model="queryParams.dateCode"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生产日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保质期" prop="expirationDate">
          <el-date-picker
            clearable
            v-model="queryParams.expirationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保质期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="组织" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入组织"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="栈板号" prop="palletNo">
          <el-input
            v-model="queryParams.palletNo"
            placeholder="请输入栈板号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item> -->
        <el-form-item label="单据类型" prop="sourceType">
          <el-select v-model="queryParams.sourceType" placeholder="请选择单据类型">
            <el-option v-for="dict in dict.type.source_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <!-- <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:transaction:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:transaction:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:transaction:remove']"
            >删除</el-button
          >
        </el-col> -->
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:transaction:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="transactionList" @selection-change="handleSelectionChange">
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
        <el-table-column label="入库单号" align="center" prop="stockInNo"
          :width="tableWidth(transactionList.map((x) => x.iqcNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip placement="top" effect="dark" :content="scope.row.stockInNo">
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.stockInNo
                }}</span>
              </el-tooltip>
              <i style="margin-left: 10px; cursor: pointer" class="el-icon-document-copy"
                v-clipboard:copy="scope.row.stockInNo" v-clipboard:success="onCopy"></i>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="入库单id" align="center" prop="stockInId" /> -->
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" width="180" />
        <el-table-column label="物料名称" align="center" prop="materialName" width="180" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="箱号" align="center" prop="boxNo" width="288" />
        <el-table-column label="箱二维码" align="center" prop="qrCode" width="288" />
        <el-table-column label="单据类型" align="center" prop="sourceType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.source_type" :value="scope.row.sourceType" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" width="180" />
        <el-table-column label="仓库名称" align="center" prop="warehouseName" width="180" />
        <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
        <el-table-column label="库区编码" align="center" prop="areaCode" width="180" />
        <el-table-column label="库区名称" align="center" prop="areaName" width="180" />
        <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
        <el-table-column label="库位编码" align="center" prop="locationCode" />
        <el-table-column label="库位名称" align="center" prop="locationName" />
        <el-table-column label="批次" align="center" prop="batchNo" />
        <el-table-column label="生产日期" align="center" prop="dateCode" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="保质期" align="center" prop="expirationDate" width="180">
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.expirationDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
        <el-table-column label="栈板号" align="center" prop="palletNo" />
        <el-table-column label="栈板二维码" align="center" prop="palletQrCode" />

        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="180">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:transaction:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:transaction:remove']">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改库存流水对话框 -->
      <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="wms库存单信息" name="1">
              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="入库单号" prop="stockInNo" style="width: 180px">
                    <el-input v-model="form.stockInNo" placeholder="请输入入库单号" />
                  </el-form-item>
                  <!-- <el-form-item label="入库单id" prop="stockInId" style="width: 240px">
            <el-input v-model="form.stockInId" placeholder="请输入入库单id" />
          </el-form-item>
          <el-form-item label="物料id" prop="materialId" style="width: 240px">
            <el-input v-model="form.materialId" placeholder="请输入物料id" />
          </el-form-item> -->
                </el-col>
                <el-col :span="6">
                  <el-form-item label="物料编码" prop="materialCode" style="width: 180px">
                    <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="物料名称" prop="materialName" style="width: 180px">
                    <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="规格型号" prop="specification" style="width: 180px">
                    <el-input v-model="form.specification" placeholder="请输入规格型号" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="单位" prop="materialUnit" style="width: 180px">
                    <el-input v-model="form.materialUnit" placeholder="请输入单位" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="数量" prop="qty" style="width: 180px">
                    <el-input v-model="form.qty" placeholder="请输入数量" />
                  </el-form-item>
                </el-col>
              </el-row>


              <el-form-item label="箱号" prop="boxNo" style="width: 240px">
                <el-input v-model="form.boxNo" placeholder="请输入箱号" />
              </el-form-item>
              <el-form-item label="箱二维码" prop="qrCode" style="width: 700px">
                <el-input v-model="form.qrCode" type="textarea" placeholder="请输入内容" />
              </el-form-item>
              <!-- <el-form-item label="仓库id" prop="warehouseId" style="width: 240px">
            <el-input v-model="form.warehouseId" placeholder="请输入仓库id" />
          </el-form-item> -->

              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="仓库编码" prop="warehouseCode" style="width: 180px">
                    <el-input v-model="form.warehouseCode" placeholder="请输入仓库编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="仓库名称" prop="warehouseName" style="width: 180px">
                    <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <!-- <el-form-item label="库区id" prop="areaId" style="width: 240px">
            <el-input v-model="form.areaId" placeholder="请输入库区id" />
          </el-form-item> -->
                  <el-form-item label="库区编码" prop="areaCode" style="width: 180px">
                    <el-input v-model="form.areaCode" placeholder="请输入库区编码" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="库区名称" prop="areaName" style="width: 180px">
                    <el-input v-model="form.areaName" placeholder="请输入库区名称" />
                  </el-form-item>
                  <!-- <el-form-item label="库位id" prop="locationId" style="width: 240px">
            <el-input v-model="form.locationId" placeholder="请输入库位id" />
          </el-form-item> -->
                </el-col>
                <el-col :span="6">
                  <el-form-item label="库位编码" prop="locationCode" style="width: 180px">
                    <el-input v-model="form.locationCode" placeholder="请输入库位编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="库位名称" prop="locationName" style="width: 180px">
                    <el-input v-model="form.locationName" placeholder="请输入库位名称" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="批次" prop="batchNo" style="width: 180px">
                    <el-input v-model="form.batchNo" placeholder="请输入批次" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="生产日期" prop="dateCode" style="width: 180px">
                    <el-date-picker clearable v-model="form.dateCode" type="date" value-format="yyyy-MM-dd"
                      placeholder="请选择生产日期">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="保质期" prop="expirationDate" style="width: 180px">
                    <el-date-picker clearable v-model="form.expirationDate" type="date" value-format="yyyy-MM-dd"
                      placeholder="请选择保质期">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>


              <el-form-item label="备注" prop="remark" style="width: 700px">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
              </el-form-item>
              <!-- <el-form-item label="组织" prop="comId" style="width: 240px">
            <el-input v-model="form.comId" placeholder="请输入组织" />
          </el-form-item> -->
              <!-- <el-form-item label="删除标志" prop="delFlag" style="width: 240px">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item> -->

              <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                  <el-form-item label="栈板号" prop="palletNo" style="width: 180px">
                    <el-input v-model="form.palletNo" placeholder="请输入栈板号" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="栈板二维码" prop="palletQrCode" style="width: 180px">
                    <el-input v-model="form.palletQrCode" type="textarea" placeholder="请输入内容" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="单据类型" prop="sourceType" style="width: 180px">
                    <el-select v-model="form.sourceType" placeholder="请选择单据类型" style="width: 240px">
                      <el-option v-for="dict in dict.type.source_type" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listTransaction,
  getTransaction,
  delTransaction,
  addTransaction,
  updateTransaction,
} from "@/api/system/transaction";

export default {
  name: "Transaction",
  dicts: ["source_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库存流水表格数据
      transactionList: [],
      activeNames: ["1", "2"],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        comId: null,
        palletNo: null,
        palletQrCode: null,
        sourceType: null,
        // 名称和编码
        searchMaterialKeyword: "",
        searchWarehouseKeyword: "",
        searchAreaKeyword: "",
        searchLocationKeyword: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询库存流水列表 */
    getList() {
      this.loading = true;
      listTransaction(this.queryParams).then((response) => {
        // this.transactionList = this.sortArrayByField(response.rows, 'createTime');
        this.transactionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        palletNo: null,
        palletQrCode: null,
        sourceType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加库存流水";
    // },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTransaction(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库存流水";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTransaction(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTransaction(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const nos = row.stockInNo || this.ids;
      this.$modal
        .confirm('是否确认删除库存流水编号为"' + nos + '"的数据项？')
        .then(function () {
          return delTransaction(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/transaction/export",
        {
          ...this.queryParams,
        },
        `库存流水_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
