import request from '@/utils/request'

// 查询OQC检验配置列表
export function listOqcconfig(query) {
  return request({
    url: '/quality/oqcconfig/list',
    method: 'get',
    params: query
  })
}

// 查询OQC检验配置详细
export function getOqcconfig(id) {
  return request({
    url: '/quality/oqcconfig/' + id,
    method: 'get'
  })
}

// 新增OQC检验配置
export function addOqcconfig(data) {
  return request({
    url: '/quality/oqcconfig',
    method: 'post',
    data: data
  })
}

// 修改OQC检验配置
export function updateOqcconfig(data) {
  return request({
    url: '/quality/oqcconfig',
    method: 'put',
    data: data
  })
}

// 删除OQC检验配置
export function delOqcconfig(id) {
  return request({
    url: '/quality/oqcconfig/' + id,
    method: 'delete'
  })
}
