import request from '@/utils/request'

// 查询采购退货列表
export function listPurchase_return(query) {
  return request({
    url: '/system/purchase_return/list',
    method: 'get',
    params: query
  })
}

// 查询采购退货详细
export function getPurchase_return(id) {
  return request({
    url: '/system/purchase_return/' + id,
    method: 'get'
  })
}

// 新增采购退货
export function addPurchase_return(data) {
  return request({
    url: '/system/purchase_return',
    method: 'post',
    data: data
  })
}

// 修改采购退货
export function updatePurchase_return(data) {
  return request({
    url: '/system/purchase_return',
    method: 'put',
    data: data
  })
}

// 删除采购退货
export function delPurchase_return(id) {
  return request({
    url: '/system/purchase_return/' + id,
    method: 'delete'
  })
}
