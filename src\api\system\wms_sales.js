import request from '@/utils/request'

// 查询销售退货列表
export function listWms_sales(query) {
  return request({
    url: '/system/wms_sales/list',
    method: 'get',
    params: query
  })
}

// 查询销售退货详细
export function getWms_sales(id) {
  return request({
    url: '/system/wms_sales/' + id,
    method: 'get'
  })
}

// 新增销售退货
export function addWms_sales(data) {
  return request({
    url: '/system/wms_sales',
    method: 'post',
    data: data
  })
}

// 修改销售退货
export function updateWms_sales(data) {
  return request({
    url: '/system/wms_sales',
    method: 'put',
    data: data
  })
}

// 删除销售退货
export function delWms_sales(id) {
  return request({
    url: '/system/wms_sales/' + id,
    method: 'delete'
  })
}
