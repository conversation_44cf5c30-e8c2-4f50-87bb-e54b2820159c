<template>
  <!-- <el-table height="52vh" v-loading="loading" :data="stock_boxList" @selection-change="handleSelectionChange">
    <el-table-column label="箱号" align="center" prop="boxNo" />
    <el-table-column label="数量" align="center" prop="qty" />
    <el-table-column label="仓库名称" align="center" prop="warehouseName" />
    <el-table-column label="库位名称" align="center" prop="locationName" />
    <el-table-column label="库区名称" align="center" prop="areaName" />
    <el-table-column label="批次" align="center" prop="batchNo" />
  </el-table> -->

  <!-- 
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" /> -->
  <el-table
    height="62vh"
    v-loading="loading"
    :data="stockOutBoxList"
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" align="center" />
    <!-- <el-table-column label="" align="center" prop="id" /> -->
    <!-- <el-table-column label="出库主表id" align="center" prop="stockOutId" /> -->
    <!-- <el-table-column label="出库单号" align="center" prop="stockOutNo" /> -->
    <!-- <el-table-column
      label="出库明细id"
      align="center"
      prop="stockOutDetailId"
    /> -->
    <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
    <el-table-column label="箱号" align="center" prop="boxNo" />
    <!-- <el-table-column label="物料编码" align="center" prop="materialCode" />
    <el-table-column label="物料名称" align="center" prop="materialName" />
    <el-table-column label="规格型号" align="center" prop="specification" />
    <el-table-column label="单位" align="center" prop="materialUnit" /> -->
    <el-table-column label="数量" align="center" prop="qty" />

    <!-- <el-table-column label="箱二维码" align="center" prop="qrCode" /> -->
    <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
    <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
    <el-table-column label="仓库名称" align="center" prop="warehouseName" />
    <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
    <el-table-column label="库区编码" align="center" prop="areaCode" />
    <el-table-column label="库区名称" align="center" prop="areaName" />
    <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
    <el-table-column label="库位编码" align="center" prop="locationCode" />
    <el-table-column label="库位名称" align="center" prop="locationName" />
    <!-- <el-table-column label="批次" align="center" prop="batchNo" /> -->
    <!-- <el-table-column label="是否暂存；" align="center" prop="isStaging" /> -->
    <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
    <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
    <!-- <el-table-column
      label="生产日期"
      align="center"
      prop="dateCode"
      width="180"
    >
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.dateCode, "{y}-{m}-{d}") }}</span>
      </template>
    </el-table-column>
    <el-table-column
      label="保质期"
      align="center"
      prop="expirationDate"
      width="180"
    >
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.expirationDate, "{y}-{m}-{d}") }}</span>
      </template>
    </el-table-column> -->
  </el-table>
</template>

<script>
import {
  listStockOutBox,
  getStockOutBox,
  delStockOutBox,
  addStockOutBox,
  updateStockOutBox,
} from "@/api/system/stockOutBox";
// import {
//   listStock_box,
//   getStock_box,
//   delStock_box,
//   addStock_box,
//   updateStock_box,
// } from "@/api/system/stockOutBox";

export default {
  props: ["stock_detail_id"],
  name: "stockBox_name",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出库单箱信息表格数据
      stockOutBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockOutId: null,
        stockOutNo: null,
        stockOutDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        comId: null,
        dateCode: null,
        expirationDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stockOutId: [
          { required: true, message: "出库主表id不能为空", trigger: "blur" },
        ],
        stockOutNo: [
          { required: true, message: "出库单号不能为空", trigger: "blur" },
        ],
        stockOutDetailId: [
          { required: true, message: "出库明细id不能为空", trigger: "blur" },
        ],
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },

  created() {
    this.getList();
    // this.handleQueryById();
    console.log("this.stock_detail_id", this.stock_detail_id);
  },
  methods: {
    /** 查询入库单箱信息列表 */
    getList() {
      // 判断 stock_detail_id 是否存在且不为空字符串
      if (this.stock_detail_id && this.stock_detail_id != "") {
        this.loading = true;
        // 如果有值，设置查询参数中的 stockOutDetailId
        this.queryParams.stockOutDetailId = this.stock_detail_id;
        listStockOutBox(this.queryParams).then((response) => {
          this.stockOutBoxList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
      this.loading = false;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockOutId: null,
        stockOutNo: null,
        stockOutDetailId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        isStaging: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        dateCode: null,
        expirationDate: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加出库单箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStockOutBox(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改出库单箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateStockOutBox(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStockOutBox(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 根据id搜索box */
    handleQueryById() {
      listStockOutBox(this.stock_detail_id)((response) => {
        this.stockOutBoxList = response.rows;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除出库单箱信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStockOutBox(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stockOutBox/export",
        {
          ...this.queryParams,
        },
        `出库单箱信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
