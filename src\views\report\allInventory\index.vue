<template>
  <div class="app-container">
    <div class="inventory-layout">
      <!-- 左侧表格部分 -->
      <div class="left-section">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item label="物料编码" prop="materialCode">
            <el-input
              v-model="queryParams.materialCode"
              placeholder="请输入物料编码"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="物料名称" prop="materialName">
            <el-input
              v-model="queryParams.materialName"
              placeholder="请输入物料名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-table
          height="62vh"
          v-loading="loading"
          :data="inventoryList"
          @row-click="handleRowClick"
        >
          <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
          <!-- <el-table-column label="入库单号" align="center" prop="stockInNo" />
          <el-table-column label="入库单id" align="center" prop="stockInId" />
          <el-table-column label="物料id" align="center" prop="materialId" /> -->
          <el-table-column
            label="物料编码"
            align="center"
            prop="materialCode"
          />
          <el-table-column
            label="物料名称"
            align="center"
            prop="materialName"
          />
          <!-- <el-table-column label="规格型号" align="center" prop="specification" /> -->
          <el-table-column label="单位" align="center" prop="materialUnit" />
          <el-table-column label="数量" align="center" prop="qty" />
          <!-- <el-table-column label="箱号" align="center" prop="boxNo" />
      <el-table-column label="箱二维码" align="center" prop="qrCode" />
      <el-table-column label="仓库id" align="center" prop="warehouseId" />
      <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="库区id" align="center" prop="areaId" />
      <el-table-column label="库区编码" align="center" prop="areaCode" />
      <el-table-column label="库区名称" align="center" prop="areaName" />
      <el-table-column label="库位id" align="center" prop="locationId" />
      <el-table-column label="库位编码" align="center" prop="locationCode" />
      <el-table-column label="库位名称" align="center" prop="locationName" />
      <el-table-column label="批次" align="center" prop="batchNo" />
      <el-table-column label="生产日期" align="center" prop="dateCode" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateCode, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保质期" align="center" prop="expirationDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
      <el-table-column label="栈板号" align="center" prop="palletNo" />
      <el-table-column label="栈板二维码" align="center" prop="palletQrCode" /> -->
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getListAll"
        />
      </div>

      <!-- 右侧表格容器 -->
      <div class="right-section">
        <!-- 右上表格 -->
        <div class="right-top">
          <!-- <div class="info-group"> -->
          <el-form :inline="true" size="small">
            <el-form-item label="物料编码">
              <el-input
                v-model="selectedMaterial.materialCode"
                readonly
                placeholder="物料编码"
                size="small"
                style="width: 100px"
              />
            </el-form-item>
            <el-form-item label="物料名称">
              <el-input
                v-model="selectedMaterial.materialName"
                readonly
                placeholder="物料名称"
                size="small"
                style="width: 100px"
              />
            </el-form-item>
            <el-form-item label="总数量">
              <el-input
                v-model="selectedMaterial.qty"
                readonly
                placeholder="数量"
                size="small"
                style="width: 100px"
              />
            </el-form-item>
          </el-form>
          <!-- </div> -->
          <el-table
            height="30vh"
            :data="inventoryListys"
            v-loading="loadingys"
            @row-click="handleRowClickys"
          >
            <!-- 根据左侧选择的row出现对应的数据 -->
            <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
            <!-- <el-table-column label="仓库编码" align="center" prop="warehouseCode" /> -->
            <el-table-column
              label="仓库名称"
              align="center"
              prop="warehouseName"
            />
            <!-- <el-table-column label="库区id" align="center" prop="areaId" /> -->
            <!-- <el-table-column label="库区编码" align="center" prop="areaCode" /> -->
            <el-table-column label="库区名称" align="center" prop="areaName" />
            <!-- <el-table-column label="库位id" align="center" prop="locationId" /> -->
            <!-- <el-table-column label="库位编码" align="center" prop="locationCode" /> -->
            <el-table-column
              label="库位名称"
              align="center"
              prop="locationName"
            />
            <el-table-column label="批次" align="center" prop="batchNo" />
            <el-table-column label="数量" align="center" prop="qty" />
          </el-table>

          <pagination
            v-show="totalys > 0"
            :total="totalys"
            :page.sync="queryParamsys.pageNum"
            :limit.sync="queryParamsys.pageSize"
            @pagination="getListys"
          />
        </div>
        <!-- 右下表格 -->
        <div class="right-bottom">
          <el-table height="30vh" :data="inventoryListyx" v-loading="loadingyx">
            <!-- 根据右上选择的row出现对应的数据 -->
            <el-table-column label="箱号" align="center" prop="boxNo" />
            <el-table-column label="箱二维码" align="center" prop="qrCode" />
            <el-table-column label="数量" align="center" prop="qty" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.inventory-layout {
  display: flex;
  height: calc(100vh - 120px);
  gap: 20px;

  .left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
  }

  .right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 0;

    .right-top,
    .right-bottom {
      flex: 1;
      background-color: #fff;
      padding: 10px;
      border-radius: 4px;
    }
  }
}

// 调整表格样式
::v-deep .el-table {
  flex: 1;
}

// 确保分页器正确显示
::v-deep .pagination-container {
  margin-top: 10px;
}
</style>

<script>
import {
  listInventory,
  listInventoryall,
  getInventory,
  delInventory,
  addInventory,
  updateInventory,
} from "@/api/system/inventory";

export default {
  name: "Inventory",
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingys: false,
      loadingyx: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      totalys: 0, // 右上表格总条数
      totalyx: 0, // 右下表格总条数

      selectedMaterial: {
        materialCode: "",
        materialName: "",
        qty: 0,
      },
      // ...其他数据
      // wms库存表格数据
      inventoryList: [],
      // 右上表格数据
      inventoryListys: [],
      // 右下表格数据
      inventoryListyx: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        comId: null,
        palletNo: null,
        palletQrCode: null,
      },
      queryParamsys: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        warehouseCode: null,
        warehouseName: null,
        areaCode: null,
        areaName: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        boxNo: [{ required: true, message: "箱号不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getListAll();
    //this.getList();
  },
  methods: {
    /** 查询wms库存列表所有物料的总数 */
    getListAll() {
      this.loading = true;
      listInventoryall(this.queryParams).then((response) => {
        this.inventoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询wms库存列表 */
    getListys() {
      this.loadingys = true;
      listInventory(this.queryParamsys).then((response) => {
        this.inventoryListys = response.rows;
        this.totalys = response.total;
        this.loadingys = false;
      });
    },
    /** 查询wms库存列表 */
    getListyx() {
      this.loadingyx = true;
      listInventory(this.queryParams).then((response) => {
        this.inventoryListyx = response.rows;
        this.totalyx = response.total;
        this.loadingyx = false;
      });
    },
    handleRowClick(row) {
      // 清空右上表格查询参数
      this.queryParamsys = {
        pageNum: 1,
        pageSize: 10,
        materialCode: row.materialCode,
        materialName: row.materialName,
        warehouseCode: null,
        warehouseName: null,
        areaCode: null,
        areaName: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
      };
      this.selectedMaterial = {
        materialCode: row.materialCode,
        materialName: row.materialName,
        qty: row.qty,
      };
      // 点击左侧表格行/右上表格行时，获取该行数据
      this.queryParams.materialCode = row.materialCode;
      this.queryParams.materialName = row.materialName;
      this.queryParams.warehouseCode = row.warehouseCode;
      this.queryParams.warehouseName = row.warehouseName;
      this.queryParams.areaCode = row.areaCode;
      this.queryParams.areaName = row.areaName;
      this.queryParams.locationCode = row.locationCode;
      this.queryParams.locationName = row.locationName;
      //this.queryParams.qty = row.qty;

      // 查询右上表格数据
      this.getListys();
    },
    handleRowClickys(row) {
      // 先清空查询参数
      this.queryParams = {
        pageNum: this.queryParams.pageNum, // 保留分页信息
        pageSize: this.queryParams.pageSize,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        comId: null,
        palletNo: null,
        palletQrCode: null,
      };
      // 点击左侧表格行/右上表格行时，获取该行数据
      this.queryParams.materialCode = row.materialCode;
      this.queryParams.materialName = row.materialName;
      this.queryParams.warehouseCode = row.warehouseCode;
      this.queryParams.warehouseName = row.warehouseName;
      this.queryParams.areaCode = row.areaCode;
      this.queryParams.areaName = row.areaName;
      this.queryParams.locationCode = row.locationCode;
      this.queryParams.locationName = row.locationName;
      this.queryParams.id = row.id;
      //this.queryParams.qty = row.qty;

      // 查询右上表格数据
      this.getListyx();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        palletNo: null,
        palletQrCode: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getListAll();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        stockInNo: null,
        stockInId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        boxNo: null,
        qrCode: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        locationId: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
        dateCode: null,
        expirationDate: null,
        comId: null,
        palletNo: null,
        palletQrCode: null,
      };
      // 重置右上表格查询参数
      this.queryParamsys = {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        warehouseCode: null,
        warehouseName: null,
        areaCode: null,
        areaName: null,
        locationCode: null,
        locationName: null,
        batchNo: null,
      };

      // 重置选中的物料信息
      this.selectedMaterial = {
        materialCode: "",
        materialName: "",
        qty: 0,
      };

      // 重置所有表格数据
      this.inventoryList = [];
      this.inventoryListys = [];
      this.inventoryListyx = [];

      // 重新加载主表数据
      this.getListAll();

      // 清空其他表格数据的总数
      this.totalys = 0;
      this.totalyx = 0;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
  },
};
</script>
