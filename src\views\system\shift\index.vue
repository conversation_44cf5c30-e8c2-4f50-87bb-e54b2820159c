<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="110px"
      >
        <el-form-item label="所属企业" prop="comId">
          <el-input
            v-model="queryParams.comId"
            placeholder="请输入所属企业"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属车间" prop="workshopId">
          <el-input
            v-model="queryParams.workshopId"
            placeholder="请输入所属车间"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="班次编号" prop="shiftId">
          <el-input
            v-model="queryParams.shiftId"
            placeholder="请输入班次编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="班次名称" prop="shiftName">
          <el-input
            v-model="queryParams.shiftName"
            placeholder="请输入班次名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="班次负责人ID" prop="leaderId">
        <el-input
          v-model="queryParams.leaderId"
          placeholder="请输入班次负责人ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="班次负责人姓名" prop="leaderName">
          <el-input
            v-model="queryParams.leaderName"
            placeholder="请输入班次负责人姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="当班开始时间" prop="startTime">
        <el-date-picker clearable
          v-model="queryParams.startTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择当班开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="当班结束时间" prop="endTime">
        <el-date-picker clearable
          v-model="queryParams.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择当班结束时间">
        </el-date-picker>
      </el-form-item> -->
        <el-form-item label="班次状态" prop="statusFlag">
          <el-input
            v-model="queryParams.statusFlag"
            placeholder="请输入班次状态"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="创建者ID" prop="createUserId">
        <el-input
          v-model="queryParams.createUserId"
          placeholder="请输入创建者ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新者ID" prop="updateUserId">
        <el-input
          v-model="queryParams.updateUserId"
          placeholder="请输入更新者ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:shift:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:shift:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:shift:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:shift:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="shiftList"
        @selection-change="handleSelectionChange"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="所属企业" align="center" prop="comId" /> -->
        <!-- <el-table-column label="所属车间" align="center" prop="workshopId" /> -->
        <el-table-column label="班次编号" align="center" prop="shiftId" />
        <el-table-column label="班次名称" align="center" prop="shiftName" />
        <el-table-column label="班次描述" align="center" prop="workshopDesc" />
        <el-table-column
          label="班次负责人姓名"
          align="center"
          prop="leaderName"
        />
        <el-table-column
          label="当班开始时间"
          align="center"
          prop="startTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.startTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当班结束时间"
          align="center"
          prop="endTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.startTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="班次状态" align="center" prop="statusFlag">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.status_flag"
              :value="scope.row.statusFlag"
            />
          </template> </el-table-column
        >/>
        <el-table-column label="备注" align="center" prop="remarkInfo" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:shift:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:shift:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改班次信息对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="所属企业" prop="comId">
                <el-input v-model="form.comId" placeholder="请输入所属企业" />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <!-- <el-form-item label="所属车间" prop="workshopId">
                <el-input
                  v-model="form.workshopId"
                  placeholder="请输入所属车间"
                />
              </el-form-item> -->
              <el-form-item label="所属车间" prop="workshopId">
                <el-select
                  v-model="form.workshopId"
                  placeholder="请选择所属车间"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in workshopOptions"
                    :key="item.id"
                    :label="item.workshopCode"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="班次名称" prop="shiftName">
                <el-input
                  v-model="form.shiftName"
                  placeholder="请输入班次名称"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="班次负责人" prop="leaderName">
                <el-input
                  v-model="form.leaderName"
                  placeholder="请输入班次负责人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开始时间" prop="startTime">
                <el-time-picker
                  clearable
                  v-model="form.startTime"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59',
                  }"
                  type="time"
                  style="width: 100%"
                  value-format="HH:mm:ss"
                  placeholder="请选择当班开始时间"
                >
                </el-time-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结束时间" prop="endTime">
                <el-time-picker
                  clearable
                  v-model="form.endTime"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59',
                  }"
                  type="time"
                  style="width: 100%"
                  value-format="HH:mm:ss"
                  placeholder="请选择当班结束时间"
                >
                </el-time-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="班次状态" prop="statusFlag">
                <el-radio-group v-model="form.statusFlag">
                  <el-radio
                    v-for="dict in dict.type.status_flag"
                    :key="dict.value"
                    :label="dict.value"
                    >{{ dict.label }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="班次描述" prop="workshopDesc">
                <el-input
                  v-model="form.workshopDesc"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remarkInfo">
                <el-input
                  v-model="form.remarkInfo"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 隐藏的表单项 -->
          <!-- <el-form-item label="班次编号" prop="shiftId" style="display: none;">
          <el-input v-model="form.shiftId" placeholder="请输入班次编号" />
        </el-form-item> -->
          <!-- <el-form-item label="班次负责人ID" prop="leaderId" style="display: none;">
          <el-input v-model="form.leaderId" placeholder="请输入班次负责人ID" />
        </el-form-item> -->
          <!-- <el-form-item label="创建者ID" prop="createUserId" style="display: none;">
          <el-input v-model="form.createUserId" placeholder="请输入创建者ID" />
        </el-form-item>
        <el-form-item label="更新者ID" prop="updateUserId" style="display: none;">
          <el-input v-model="form.updateUserId" placeholder="请输入更新者ID" />
        </el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listShift,
  getShift,
  delShift,
  addShift,
  updateShift,
} from "@/api/system/shift";
import { options } from "runjs";
import { listWorkshop } from "@/api/system/workshop";
export default {
  name: "Shift",
  dicts: ["status_flag"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      workshopOptions: [], // 添加车间选项数组
      // 总条数
      total: 0,
      // 班次信息表格数据
      shiftList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        comId: null,
        workshopId: null,
        shiftId: null,
        shiftName: null,
        workshopDesc: null,
        leaderId: null,
        leaderName: null,
        startTime: null,
        endTime: null,
        statusFlag: null,
        remarkInfo: null,
        isDeleted: null,
        createUserId: null,
        updateUserId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {

        workshopId: [
          { required: true, message: "所属车间不能为空", trigger: "blur" },
        ],
        shiftName: [
          { required: true, message: "班次名称不能为空", trigger: "blur" },
        ],
        leaderName: [
          { required: true, message: "班次负责人不能为空", trigger: "blur" },
        ],
        startTime: [
          { required: true, message: "请选择开始时间", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              const endTime = this.form.endTime;
              if (!endTime) {
                callback();
                return;
              }

              // 转换为分钟进行比较
              const startMinutes = this.timeToMinutes(value);
              const endMinutes = this.timeToMinutes(endTime);

              if (startMinutes >= endMinutes) {
                callback(new Error("开始时间必须小于结束时间"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        endTime: [
          { required: true, message: "请选择结束时间", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              const startTime = this.form.startTime;
              if (!startTime) {
                callback();
                return;
              }

              // 转换为分钟进行比较
              const startMinutes = this.timeToMinutes(startTime);
              const endMinutes = this.timeToMinutes(value);

              if (endMinutes <= startMinutes) {
                callback(new Error("结束时间必须大于开始时间"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        isDeleted: [
          {
            required: true,
            message: "删除标志   1：删除； 0:未删除；不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询班次信息列表 */
    getList() {
      this.loading = true;
      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: "createTime",
        isAsc: "desc",
      };
      listShift(this.queryParams).then((response) => {
        console.log("API 返回数据：", response.rows);
        this.shiftList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取车间列表 */
    getWorkshopList() {
      listWorkshop({ pageNum: 1, pageSize: 999 }).then((response) => {
        this.workshopOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        comId: null,
        workshopId: null,
        shiftId: null,
        shiftName: null,
        workshopDesc: null,
        leaderId: null,
        leaderName: null,
        startTime: null,
        endTime: null,
        statusFlag: null,
        remarkInfo: null,
        isDeleted: null,
        createUserId: null,
        createTime: null,
        updateUserId: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.codes = selection.map((item) => item.shiftId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getWorkshopList(); // 获取车间列表

      // 设置班次状态默认值为"正常"
      const normalStatus = this.dict.type.status_flag.find(
        (item) => item.label === "正常"
      );
      this.form.statusFlag = normalStatus ? normalStatus.value : "0";

      this.open = true;
      this.title = "添加班次信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.getWorkshopList();
      getShift(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改班次信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateShift(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addShift(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const codes = row.shiftId || this.codes;
      this.$modal
        .confirm('是否确认删除班次编号为"' + codes + '"的数据项？')
        .then(function () {
          return delShift(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/shift/export",
        {
          ...this.queryParams,
        },
        `班次信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    // 将时间字符串转换为分钟数
    timeToMinutes(timeStr) {
      if (!timeStr) return 0;
      const [hours, minutes, seconds] = timeStr.split(":").map(Number);
      return hours * 60 + minutes + seconds / 60;
    },
  },
};
</script>
