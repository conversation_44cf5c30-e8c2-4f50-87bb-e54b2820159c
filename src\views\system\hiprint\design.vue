<template>
  <div class="app-container">
    <div class="app-container-div">
      <!-- <el-form> -->
      <h1>【{{form.name}}】模板设计</h1>
      <iframe
        :src="src"
        width="100%"
        :height="scrollY + 'px'"
        ref="iframe"
      ></iframe>

      <!-- </el-form> -->
    </div>
  </div>
</template>
    <script>
import { updateHiprint } from "@/api/system/hiprint";
export default {
  name: "design",
  data() {
    return {
      iframeWin: {},

      src: "/hiprint/custom.html",
      visible: false,
      loading: false,
      printData: null,
      printJson: null,
      scrollY: 0,
      channel: null,
      form: {},
    };
  },
  mounted() {
    const json = {
      panels: [
        {
          index: 0,
          height: 297,
          width: 210,
          paperHeader: 49.5,
          paperFooter: 780,
          printElements: [
            {
              options: { left: 105, top: 111, height: 345, width: 442.5 },
              printElementType: { type: "rect" },
            },
            {
              options: { left: 303, top: 114, height: 339, width: 9 },
              printElementType: { type: "vline" },
            },
            {
              options: {
                left: 319.5,
                top: 159,
                height: 9.75,
                width: 120,
                field: "materialCode",
                fontSize: 19.5,
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 162,
                top: 159,
                height: 9.75,
                width: 120,
                title: "物料编号：",
                fontSize: 20.25,
                fontWeight: "bold",
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 319.5,
                top: 198,
                height: 9.75,
                width: 120,
                field: "materialName",
                fontSize: 19.5,
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 162,
                top: 199.5,
                height: 9.75,
                width: 120,
                title: "物料名称：",
                fontSize: 20.25,
                fontWeight: "bold",
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 319.5,
                top: 247.5,
                height: 9.75,
                width: 120,
                field: "dateCode",
                fontSize: 19.5,
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 162,
                top: 247.5,
                height: 9.75,
                width: 120,
                title: "生产日期",
                fontSize: 20.25,
                fontWeight: "bold",
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 318,
                top: 289.5,
                height: 9.75,
                width: 120,
                field: "batchNo",
                fontSize: 19.5,
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 163.5,
                top: 291,
                height: 9.75,
                width: 120,
                title: "批次：",
                fontSize: 20.25,
                fontWeight: "bold",
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 318,
                top: 336,
                height: 9.75,
                width: 120,
                field: "boxNo",
                fontSize: 19.5,
              },
              printElementType: { type: "text" },
            },
            {
              options: {
                left: 162,
                top: 336,
                height: 9.75,
                width: 120,
                title: "箱号：",
                fontSize: 20.25,
                fontWeight: "bold",
              },
              printElementType: { type: "text" },
            },
          ],
          paperNumberLeft: 565.5,
          paperNumberTop: 819,
        },
      ],
    };
    console.log(JSON.stringify(json), "json");
    this.iframeWin = this.$refs.iframe.contentWindow;
    this.sendIframeWinpMessage();
    window.addEventListener("message", this.handleMessage);
  },
  created() {
    console.log(this.$route.query, "this.$route.query");
    this.printJson = this.$route.query.printJson;
    this.form = this.$route.query;
    this.channel = new BroadcastChannel("zhht_print_channel");
    this.scrollY = document.documentElement.clientHeight - 140;
    console.log(this.scrollY, "this.scrollY");
  },
  methods: {
    handleMessage(e) {
      // 获取从iframe页面中传过来的值
      if (e.data.type == "jsonData") {
        //
        console.log(e.data.printJson, 777, "接收到了数据");
        this.form.printJson = e.data.printJson;
        updateHiprint(this.form).then((response) => {
          sessionStorage.setItem("printJson", null);
          this.$modal.msgSuccess("保存成功");
          this.$emit("submit",null)
          // 关闭当前标签页并返回上个页面
          const obj = {
            path: "/system/hiprint",
            query: { t: Date.now() },
          };
          this.$tab.closeOpenPage(obj);
          this.$router.push(obj);
        });
      }
    },
    sendIframeWinpMessage() {
      const printJson = this.printJson;
      const printData = null;
      sessionStorage.setItem(
        "nowPrintTemplate",
        JSON.stringify({ printJson, printData })
      );
      //   this.iframeWin.postMessage(
      //     {
      //         printJson:
      //         this.printJson /*在iframe页面中接收通过key也就是param接收，因此传输的数据可以是对象，包含多个key以及对应的数据*/,
      //     },
      //     "*"
      //   );
    },

    ok() {},
    cancel() {
      sessionStorage.removeItem("nowPrintTemplate");
      this.visible = false;
    },
  },
};
</script>
    <style scoped></style>