import request from '@/utils/request'

// 查询库区类型列表
export function listStorageType(query) {
  return request({
    url: '/system/storageType/list',
    method: 'get',
    params: query
  })
}

// 查询库区类型详细
export function getStorageType(id) {
  return request({
    url: '/system/storageType/' + id,
    method: 'get'
  })
}

// 新增库区类型
export function addStorageType(data) {
  return request({
    url: '/system/storageType',
    method: 'post',
    data: data
  })
}

// 修改库区类型
export function updateStorageType(data) {
  return request({
    url: '/system/storageType',
    method: 'put',
    data: data
  })
}

// 删除库区类型
export function delStorageType(id) {
  return request({
    url: '/system/storageType/' + id,
    method: 'delete'
  })
}
