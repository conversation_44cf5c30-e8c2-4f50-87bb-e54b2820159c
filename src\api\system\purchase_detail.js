import request from '@/utils/request'

// 查询采购单明细列表
export function listPurchase_detail(query) {
  return request({
    url: '/system/purchase_detail/list',
    method: 'get',
    params: query
  })
}

// 查询采购单明细详细
export function getPurchase_detail(id) {
  return request({
    url: '/system/purchase_detail/' + id,
    method: 'get'
  })
}

// 新增采购单明细
export function addPurchase_detail(data) {
  return request({
    url: '/system/purchase_detail',
    method: 'post',
    data: data
  })
}

// 修改采购单明细
export function updatePurchase_detail(data) {
  return request({
    url: '/system/purchase_detail',
    method: 'put',
    data: data
  })
}

// 删除采购单明细
export function delPurchase_detail(id) {
  return request({
    url: '/system/purchase_detail/' + id,
    method: 'delete'
  })
}
