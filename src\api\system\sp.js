import request from '@/utils/request'

// 查询设备巡检方案列表
export function listSp(query) {
  return request({
    url: '/system/sp/list',
    method: 'get',
    params: query
  })
}

// 查询设备巡检方案详细
export function getSp(id) {
  return request({
    url: '/system/sp/' + id,
    method: 'get'
  })
}

// 新增设备巡检方案
export function addSp(data) {
  return request({
    url: '/system/sp',
    method: 'post',
    data: data
  })
}

// 修改设备巡检方案
export function updateSp(data) {
  return request({
    url: '/system/sp',
    method: 'put',
    data: data
  })
}

// 删除设备巡检方案
export function delSp(id) {
  return request({
    url: '/system/sp/' + id,
    method: 'delete'
  })
}
