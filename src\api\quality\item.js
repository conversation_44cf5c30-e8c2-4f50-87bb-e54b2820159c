import request from '@/utils/request'

// 查询IQC检验项目

export function listItem(query) {
  return request({
    url: '/quality/item/list',
    method: 'get',
    params: query
  })
}

// 查询IQC检验项目
export function getItem(id) {
  return request({
    url: '/quality/item/' + id,
    method: 'get'

  })
}
// export function listItemByTemplate(ids) {

//   // 从 query 对象中获取 ids 数组
//   const templateIds = ids || [];
//   const templateIdsPath = templateIds.join('/');
//   return request({
//     url: `/quality/info/ids/${templateIdsPath}`,
//     method: 'get',
//   });
// }
// export function listItemByTemplate(ids) {
//   return request({
//     url: '/quality/info/item', // 固定路径，不再拼接ID
//     method: 'get',
//     params: {
//       ids: ids // 自动序列化为 ?ids=id1&id=id2
//     }
//   });
// }
export function listItemByTemplate(ids) {
  return request({
    url: '/quality/info/item',
    method: 'post', // 改为 POST 请求
    data: ids
  });
}
// export function listItemByTemplate(ids) {
//   const idsPath = ids.join('/'); // 将数组拼接为 "id1/id2/id3"
//   return request({
//     url: `/quality/info/item/${idsPath}`, // 路径格式：/info/item/id1/id2
//     method: 'get'
//   });
// }
export function listItemByClass(query) {
  return request({
    url: '/quality/item/class',
    method: 'get',
    params: query
  })
}

// 新增IQC检验项目

export function addItem(data) {
  return request({
    url: '/quality/item',
    method: 'post',
    data: data
  })
}

// 修改IQC检验项目

export function updateItem(data) {
  return request({
    url: '/quality/item',
    method: 'put',
    data: data
  })
}

// 删除IQC检验项目

export function delItem(id) {
  return request({
    url: '/quality/item/' + id,
    method: 'delete'
  })
}
