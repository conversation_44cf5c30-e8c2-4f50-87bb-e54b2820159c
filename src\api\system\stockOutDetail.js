import request from '@/utils/request'

// 查询出库单明细列表
export function listStockOutDetail(query) {
  return request({
    url: '/system/stockOutDetail/list',
    method: 'get',
    params: query
  })
}

// 查询出库单明细列表-出库类型
export function listStockOutDetailType(query) {
  return request({
    url: '/system/stockOutDetail/listOutType',
    method: 'get',
    params: query
  })
}

// 查询出库单明细详细
export function getStockOutDetail(id) {
  return request({
    url: '/system/stockOutDetail/' + id,
    method: 'get'
  })
}

// 新增出库单明细
export function addStockOutDetail(data) {
  return request({
    url: '/system/stockOutDetail',
    method: 'post',
    data: data
  })
}

// 修改出库单明细
export function updateStockOutDetail(data) {
  return request({
    url: '/system/stockOutDetail',
    method: 'put',
    data: data
  })
}

// 删除出库单明细
export function delStockOutDetail(id) {
  return request({
    url: '/system/stockOutDetail/' + id,
    method: 'delete'
  })
}
