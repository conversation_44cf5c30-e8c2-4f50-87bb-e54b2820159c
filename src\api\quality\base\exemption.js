import request from '@/utils/request'

// 查询物料免检清单列表
export function listExemption(query) {
  return request({
    url: '/quality/exemption/list',
    method: 'get',
    params: query
  })
}

// 查询物料免检清单详细
export function getExemption(id) {
  return request({
    url: '/quality/exemption/' + id,
    method: 'get'
  })
}

// 新增物料免检清单
export function addExemption(data) {
  return request({
    url: '/quality/exemption',
    method: 'post',
    data: data
  })
}

// 修改物料免检清单
export function updateExemption(data) {
  return request({
    url: '/quality/exemption',
    method: 'put',
    data: data
  })
}

// 删除物料免检清单
export function delExemption(id) {
  return request({
    url: '/quality/exemption/' + id,
    method: 'delete'
  })
}
